package cn.iocoder.yudao.module.member.dal.mysql.subscribe;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserPageApiReqDTO;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserRespDTO;
import cn.iocoder.yudao.module.member.dal.dataobject.subscribe.SubscribeDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.member.controller.admin.subscribe.vo.*;

/**
 * 用户订阅 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SubscribeMapper extends BaseMapperX<SubscribeDO> {

    default PageResult<SubscribeDO> selectPage(SubscribePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SubscribeDO>()
                .likeIfPresent(SubscribeDO::getName, reqVO.getName())
                .likeIfPresent(SubscribeDO::getEmail, reqVO.getEmail())
                .eqIfPresent(SubscribeDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SubscribeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SubscribeDO::getId));
    }

    default void deleteByEmail(String email) {
        delete(new LambdaQueryWrapperX<SubscribeDO>()
                .eq(SubscribeDO::getEmail, email));
    }

    default PageResult<SubscribeDO> selectSubscribeUserPage(SubscribeUserPageApiReqDTO pageReqVO){
        return selectPage(pageReqVO, new LambdaQueryWrapperX<SubscribeDO>()
                .eqIfPresent(SubscribeDO::getStatus, pageReqVO.getStatus())
                .orderByDesc(SubscribeDO::getId));
    };
}