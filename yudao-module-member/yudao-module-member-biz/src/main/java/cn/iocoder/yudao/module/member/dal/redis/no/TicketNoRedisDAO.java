package cn.iocoder.yudao.module.member.dal.redis.no;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.module.member.dal.redis.RedisKeyConstants;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @program: ruoyi-vue-pro
 * @description: 工单序号的Redis DAO
 * @author: DingXiao
 * @create: 2025-08-04 13:05
 **/
@Repository
public class TicketNoRedisDAO {
    public static final String TICKET_NO_PREFIX = "T";



    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public String generate(String prefix) {
        // 递增序号
        String noPrefix = prefix + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        String key = RedisKeyConstants.TICKET_NO + noPrefix;
        Long no = stringRedisTemplate.opsForValue().increment(key);
        // 设置过期时间
        stringRedisTemplate.expire(key, Duration.ofMinutes(1L));
        return noPrefix + no;
    }
}
