package cn.iocoder.yudao.module.member.convert.adtracking;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingRespVO;
import cn.iocoder.yudao.module.member.dal.dataobject.adtracking.MemberAdTrackingDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 会员广告跟踪 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberAdTrackingConvert {

    MemberAdTrackingConvert INSTANCE = Mappers.getMapper(MemberAdTrackingConvert.class);

    AppMemberAdTrackingRespVO convert(MemberAdTrackingDO bean);

    List<AppMemberAdTrackingRespVO> convertList(List<MemberAdTrackingDO> list);

    PageResult<AppMemberAdTrackingRespVO> convertPage(PageResult<MemberAdTrackingDO> page);

}
