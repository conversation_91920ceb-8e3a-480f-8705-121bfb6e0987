package cn.iocoder.yudao.module.member.service.captcha;

import cn.iocoder.yudao.module.member.controller.app.captcha.vo.AppCaptchaImageRespVO;

/**
 * 用户端验证码 Service 接口
 *
 * <AUTHOR>
 */
public interface AppCaptchaService {

    /**
     * 生成验证码图片
     *
     * @return 验证码图片信息
     */
    AppCaptchaImageRespVO getCaptchaImage();

    /**
     * 验证验证码
     *
     * @param key 验证码key
     * @param code 验证码值
     * @return 是否验证成功
     */
    boolean validateCaptcha(String key, String code);

    /**
     * 删除验证码
     *
     * @param key 验证码key
     */
    void deleteCaptcha(String key);
}
