package cn.iocoder.yudao.module.game.service.category;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.game.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.game.dal.dataobject.category.GameCategoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 游戏分类 Service 接口
 *
 * <AUTHOR>
 */
public interface GameCategoryService {

    /**
     * 创建游戏分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid GameCategorySaveReqVO createReqVO);

    /**
     * 更新游戏分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid GameCategorySaveReqVO updateReqVO);

    /**
     * 删除游戏分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得游戏分类
     *
     * @param id 编号
     * @return 游戏分类
     */
    GameCategoryDO getCategory(Long id);

    /**
     * 获得游戏分类分页
     *
     * @param pageReqVO 分页查询
     * @return 游戏分类分页
     */
    PageResult<GameCategoryDO> getCategoryPage(GameCategoryPageReqVO pageReqVO);

    /**
     * 获取分类列表
     * @param listReqVO
     * @return
     */
    List<GameCategoryDO> getCategoryList(GameCategoryListReqVO listReqVO);

    /**
     * 获取分类列表
     * @param ids
     * @return
     */
    List<GameCategoryDO> getCategoryListByIds(Collection<Long> ids);
}