package cn.iocoder.yudao.module.game.service.rating;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.game.controller.app.rating.vo.*;
import cn.iocoder.yudao.module.game.dal.dataobject.rating.GameRatingDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 游戏评分 Service 接口
 *
 * <AUTHOR>
 */
public interface GameRatingService {

    /**
     * 创建游戏评分
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRating(@Valid AppGameRatingSaveReqVO createReqVO);

    /**
     * 更新游戏评分
     *
     * @param updateReqVO 更新信息
     */
    void updateRating(@Valid AppGameRatingSaveReqVO updateReqVO);

    /**
     * 删除游戏评分
     *
     * @param id 编号
     */
    void deleteRating(Long id);

    /**
     * 获得游戏评分
     *
     * @param id 编号
     * @return 游戏评分
     */
    GameRatingDO getRating(Long id);

    /**
     * 获得游戏评分分页
     *
     * @param pageReqVO 分页查询
     * @return 游戏评分分页
     */
    PageResult<GameRatingDO> getRatingPage(AppGameRatingPageReqVO pageReqVO);

}