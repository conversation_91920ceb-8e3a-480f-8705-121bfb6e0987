package cn.iocoder.yudao.module.game.dal.mysql.tag;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.game.dal.dataobject.tag.GameTagDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.game.controller.admin.tag.vo.*;

/**
 * 游戏标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GameTagMapper extends BaseMapperX<GameTagDO> {

    default PageResult<GameTagDO> selectPage(GameTagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GameTagDO>()
                .likeIfPresent(GameTagDO::getName, reqVO.getName())
                .orderByDesc(GameTagDO::getId));
    }

}