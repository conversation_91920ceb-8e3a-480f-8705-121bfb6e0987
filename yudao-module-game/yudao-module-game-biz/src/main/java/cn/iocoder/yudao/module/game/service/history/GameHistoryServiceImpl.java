package cn.iocoder.yudao.module.game.service.history;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.game.controller.app.history.vo.*;
import cn.iocoder.yudao.module.game.dal.dataobject.history.GameHistoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.game.dal.mysql.history.GameHistoryMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.game.enums.ErrorCodeConstants.*;

/**
 * 游戏历史 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GameHistoryServiceImpl implements GameHistoryService {

    @Resource
    private GameHistoryMapper historyMapper;

    @Override
    public Long createHistory(AppGameHistorySaveReqVO createReqVO) {
        // 插入
        GameHistoryDO history = BeanUtils.toBean(createReqVO, GameHistoryDO.class);
        historyMapper.insert(history);
        // 返回
        return history.getId();
    }

    @Override
    public void updateHistory(AppGameHistorySaveReqVO updateReqVO) {
        // 校验存在
        validateHistoryExists(updateReqVO.getId());
        // 更新
        GameHistoryDO updateObj = BeanUtils.toBean(updateReqVO, GameHistoryDO.class);
        historyMapper.updateById(updateObj);
    }

    @Override
    public void deleteHistory(Long id) {
        // 校验存在
        validateHistoryExists(id);
        // 删除
        historyMapper.deleteById(id);
    }

    private void validateHistoryExists(Long id) {
        if (historyMapper.selectById(id) == null) {
            throw exception(GAME_HISTORY_NOT_EXISTS);
        }
    }

    @Override
    public GameHistoryDO getHistory(Long id) {
        return historyMapper.selectById(id);
    }

    @Override
    public PageResult<GameHistoryDO> getHistoryPage(AppGameHistoryPageReqVO pageReqVO) {
        return historyMapper.selectPage(pageReqVO);
    }

}