package cn.iocoder.yudao.module.pay.api.rate.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: ruoyi-vue-pro
 * @description:
 * @author: Ding<PERSON>ia<PERSON>
 * @create: 2025-04-10 19:13
 **/
@Data
public class PayExchangeRateRespDTO {

    /**
     * 目标货币
     */
    private String currency;
    /**
     * 符号
     */
    private String symbol;
    /**
     * 货币名称
     */
    private String currencyName;
    /**
     * 汇率值
     */
    private BigDecimal rate;
}
