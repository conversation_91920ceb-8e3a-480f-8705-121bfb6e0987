package cn.iocoder.yudao.module.pay.api.rate;

import cn.iocoder.yudao.module.pay.api.rate.dto.PayExchangeRateRespDTO;

import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 汇率API接口
 * @author: Ding<PERSON>ia<PERSON>
 * @create: 2025-04-10 18:29
 **/
public interface PayExchangeRateApi {

    List<PayExchangeRateRespDTO> getExchangeRateList();

    PayExchangeRateRespDTO getExchangeRate(String currency);
}
