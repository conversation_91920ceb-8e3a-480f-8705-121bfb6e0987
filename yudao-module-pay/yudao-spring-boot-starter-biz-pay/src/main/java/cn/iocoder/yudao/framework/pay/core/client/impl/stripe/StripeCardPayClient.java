package cn.iocoder.yudao.framework.pay.core.client.impl.stripe;

import cn.iocoder.yudao.framework.pay.core.client.dto.order.PayOrderRespDTO;
import cn.iocoder.yudao.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import cn.iocoder.yudao.framework.pay.core.enums.channel.PayChannelEnum;
import cn.iocoder.yudao.framework.pay.core.enums.order.PayOrderDisplayModeEnum;
import com.stripe.model.checkout.Session;
import com.stripe.param.checkout.SessionCreateParams;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * @program: ruoyi-vue-pro
 * @description: Stripe 银行卡的支付方式
 * @author: DingXiao
 * @create: 2025-02-07 18:13
 **/
@Slf4j
public class StripeCardPayClient extends AbstractStripeClient{


    public StripeCardPayClient(Long channelId, StripeClientConfig config) {
        super(channelId, PayChannelEnum.STRIPE_CARD.getCode(), config);
    }


    @Override
    protected Optional<SessionCreateParams.PaymentMethodType> getPaymentMethodType() {
        return Optional.of(SessionCreateParams.PaymentMethodType.CARD);
    }
}
