package cn.iocoder.yudao.module.agent.service.shipping;

import cn.iocoder.yudao.module.agent.enums.WeightCompareTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ShippingQuoteServiceImpl 测试类
 * 主要测试运费计算相关的功能
 *
 * <AUTHOR>
 */
class ShippingQuoteServiceImplTest {

    @Test
    @DisplayName("测试重量比较类型枚举 - MAX规则")
    void testWeightCompareTypeEnum_MAX() {
        WeightCompareTypeEnum maxType = WeightCompareTypeEnum.MAX;
        
        // 测试体积重大于实际重量的情况
        Integer result1 = maxType.calculateChargeableWeight(1000, 1500);
        assertEquals(1500, result1, "MAX规则应该取较大者");
        
        // 测试实际重量大于体积重的情况
        Integer result2 = maxType.calculateChargeableWeight(2000, 1500);
        assertEquals(2000, result2, "MAX规则应该取较大者");
        
        // 测试相等的情况
        Integer result3 = maxType.calculateChargeableWeight(1500, 1500);
        assertEquals(1500, result3, "MAX规则相等时应该返回该值");
    }

    @Test
    @DisplayName("测试重量比较类型枚举 - DOUBLE_THRESHOLD规则")
    void testWeightCompareTypeEnum_DOUBLE_THRESHOLD() {
        WeightCompareTypeEnum doubleThresholdType = WeightCompareTypeEnum.DOUBLE_THRESHOLD;
        
        // 测试体积重小于实际重量2倍的情况
        Integer result1 = doubleThresholdType.calculateChargeableWeight(1000, 1500);
        assertEquals(1000, result1, "体积重1500 < 实际重量2倍2000，应该使用实际重量");
        
        // 测试体积重等于实际重量2倍的情况
        Integer result2 = doubleThresholdType.calculateChargeableWeight(1000, 2000);
        assertEquals(2000, result2, "体积重2000 = 实际重量2倍2000，应该使用体积重");
        
        // 测试体积重大于实际重量2倍的情况
        Integer result3 = doubleThresholdType.calculateChargeableWeight(1000, 2500);
        assertEquals(2500, result3, "体积重2500 > 实际重量2倍2000，应该使用体积重");
    }

    @Test
    @DisplayName("测试重量比较类型枚举 - BUBBLE_RATIO规则")
    void testWeightCompareTypeEnum_BUBBLE_RATIO() {
        WeightCompareTypeEnum bubbleRatioType = WeightCompareTypeEnum.BUBBLE_RATIO;

        // 测试≤2kg货物，泡重比≤1.5的情况（免泡，按实际重量计费）
        Integer result1 = bubbleRatioType.calculateChargeableWeight(1500, 2000); // 泡重比 = 2000/1500 = 1.33 ≤ 1.5
        assertEquals(1500, result1, "≤2kg货物，泡重比≤1.5时应该按实际重量计费");

        Integer result2 = bubbleRatioType.calculateChargeableWeight(2000, 3000); // 泡重比 = 3000/2000 = 1.5 = 1.5
        assertEquals(2000, result2, "≤2kg货物，泡重比=1.5时应该按实际重量计费");

        // 测试≤2kg货物，泡重比>1.5的情况（按较大者计费）
        Integer result3 = bubbleRatioType.calculateChargeableWeight(1500, 3000); // 泡重比 = 3000/1500 = 2.0 > 1.5
        assertEquals(3000, result3, "≤2kg货物，泡重比>1.5时应该按较大者计费");

        Integer result4 = bubbleRatioType.calculateChargeableWeight(1000, 2000); // 泡重比 = 2000/1000 = 2.0 > 1.5
        assertEquals(2000, result4, "≤2kg货物，泡重比>1.5时应该按较大者计费");

        // 测试>2kg货物的情况（直接按较大者计费）
        Integer result5 = bubbleRatioType.calculateChargeableWeight(2500, 3000); // 2500g > 2kg，取较大者
        assertEquals(3000, result5, ">2kg货物应该直接按较大者计费");

        Integer result6 = bubbleRatioType.calculateChargeableWeight(2500, 2000); // 2500g > 2kg，取较大者
        assertEquals(2500, result6, ">2kg货物应该直接按较大者计费");

        // 测试边界情况：正好2kg
        Integer result7 = bubbleRatioType.calculateChargeableWeight(2000, 2500); // 2000g = 2kg，泡重比 = 2500/2000 = 1.25 ≤ 1.5
        assertEquals(2000, result7, "=2kg货物，泡重比≤1.5时应该按实际重量计费");
    }

    @Test
    @DisplayName("测试重量比较类型枚举 - 边界情况")
    void testWeightCompareTypeEnum_EdgeCases() {
        WeightCompareTypeEnum maxType = WeightCompareTypeEnum.MAX;
        
        // 测试实际重量为null的情况
        Integer result1 = maxType.calculateChargeableWeight(null, 1500);
        assertEquals(1500, result1, "实际重量为null时应该使用体积重");
        
        // 测试体积重为null的情况
        Integer result2 = maxType.calculateChargeableWeight(1000, null);
        assertEquals(1000, result2, "体积重为null时应该使用实际重量");
        
        // 测试都为null的情况
        Integer result3 = maxType.calculateChargeableWeight(null, null);
        assertEquals(0, result3, "都为null时应该返回0");
        
        // 测试实际重量为0的情况
        Integer result4 = maxType.calculateChargeableWeight(0, 1500);
        assertEquals(1500, result4, "实际重量为0时应该使用体积重");
    }

    @Test
    @DisplayName("测试根据编码获取枚举")
    void testGetByCode() {
        // 测试有效编码
        assertEquals(WeightCompareTypeEnum.MAX, WeightCompareTypeEnum.getByCode("MAX"));
        assertEquals(WeightCompareTypeEnum.MAX, WeightCompareTypeEnum.getByCode("max"));
        assertEquals(WeightCompareTypeEnum.DOUBLE_THRESHOLD, WeightCompareTypeEnum.getByCode("DOUBLE_THRESHOLD"));
        assertEquals(WeightCompareTypeEnum.DOUBLE_THRESHOLD, WeightCompareTypeEnum.getByCode("double_threshold"));
        assertEquals(WeightCompareTypeEnum.BUBBLE_RATIO, WeightCompareTypeEnum.getByCode("BUBBLE_RATIO"));
        assertEquals(WeightCompareTypeEnum.BUBBLE_RATIO, WeightCompareTypeEnum.getByCode("bubble_ratio"));

        // 测试无效编码
        assertNull(WeightCompareTypeEnum.getByCode("INVALID"));
        assertNull(WeightCompareTypeEnum.getByCode(null));
        assertNull(WeightCompareTypeEnum.getByCode(""));
    }

    @Test
    @DisplayName("测试编码有效性验证")
    void testIsValidCode() {
        // 测试有效编码
        assertTrue(WeightCompareTypeEnum.isValidCode("MAX"));
        assertTrue(WeightCompareTypeEnum.isValidCode("max"));
        assertTrue(WeightCompareTypeEnum.isValidCode("DOUBLE_THRESHOLD"));
        assertTrue(WeightCompareTypeEnum.isValidCode("double_threshold"));
        assertTrue(WeightCompareTypeEnum.isValidCode("BUBBLE_RATIO"));
        assertTrue(WeightCompareTypeEnum.isValidCode("bubble_ratio"));

        // 测试无效编码
        assertFalse(WeightCompareTypeEnum.isValidCode("INVALID"));
        assertFalse(WeightCompareTypeEnum.isValidCode(null));
        assertFalse(WeightCompareTypeEnum.isValidCode(""));
    }
}
