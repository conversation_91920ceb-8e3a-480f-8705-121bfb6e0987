package cn.iocoder.yudao.module.agent.dal.dataobject.stock;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代购仓库 DO
 *
 * <AUTHOR>
 */
@TableName(value = "agent_stock", autoResultMap = true)
@KeySequence("agent_stock_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentStockDO extends BaseDO {

    /**
     * 品牌编号
     */
    @TableId
    private Long id;
    /**
     * 仓库编号
     */
    private Long warehouseId;

    /**
     * 订单编号
     *
     */
    private Long orderId;

    /**
     * 订单项编号
     *
     */
    private Long orderItemId;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 分类编号
     */
    private Long categoryId;
    /**
     * spu编号
     */
    private Long spuId;
    /**
     * 商品 SPU 名称
     */
    private String spuName;
    /**
     * 商品 SKU 编号
     */
    private Long skuId;
    /**
     * 商品属性数组，JSON 格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Property> properties;
    /**
     * 商品图片
     */
    private String picUrl;
    /**
     * 库存数量
     */
    private Integer count;
    /**
     * 商品重量 单位：g
     */
    private Integer weight;
    /**
     * 商品体积
     */
    private BigDecimal volume;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 预包装重量
     */
    private Integer prePackageWeight;
    /**
     * 入库质检图片
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String>  inspectPicUrls;

    /**
     * 入库视频
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String>  inspectVideoUrls;
    /**
     * 相关文件地址
     */
    private String fileUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 入库日期
     */
    private LocalDateTime inTime;
    /**
     * 到期日期
     */
    private LocalDateTime expiredTime;

    /**
     * 位置
     */
    private String location;

    /**
     * 是否可合并
     */
    private Boolean canCombine;

    /**
     * 商品属性
     */
    @Data
    public static class Property implements Serializable {

        /**
         * 属性编号
         *
         * 关联 ProductPropertyDO 的 id 编号
         */
        private Long propertyId;
        /**
         * 属性名字
         *
         * 关联 ProductPropertyDO 的 name 字段
         */
        private String propertyName;

        /**
         * 属性值编号
         *
         * 关联 ProductPropertyValueDO 的 id 编号
         */
        private Long valueId;
        /**
         * 属性值名字
         *
         * 关联 ProductPropertyValueDO 的 name 字段
         */
        private String valueName;

    }

}