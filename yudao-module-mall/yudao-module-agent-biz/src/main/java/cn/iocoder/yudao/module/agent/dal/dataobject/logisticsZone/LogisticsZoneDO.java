package cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代购物流国家分区 DO
 *
 * <AUTHOR>
 */
@TableName("agent_logistics_zone")
@KeySequence("agent_logistics_zone_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsZoneDO extends BaseDO {

    /**
     * 分类编号
     */
    @TableId
    private Long id;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 产品编号（null表示全部产品适用）
     * 支持多个产品ID，JSON格式存储，如：[1,2,3]
     */
    private String productIds;


    /**
     * 分区编码(FORBIDDEN-禁止配送, REMOTE-偏远地区, NORMAL-正常,1,2,3,4等具体可用分区),
     */
    private String zoneCode;
    /**
     * 分区名称
     */
    private String zoneName;
    /**
     * 一级行政区划 (州/省/自治区等)
     */
    private String stateProvince;
    /**
     * 二级行政区划 (城市/地区等)
     */
    private String city;
    /**
     * 三级行政区划 (区/县等)
     */
    private String district;
    /**
     * 特殊区域类型 (ISLAND-岛屿, TERRITORY-领土, MILITARY-军事基地等)
     */
    private String specialAreaType;
    /**
     * 完整区域描述 (用于显示和搜索，如：California Los Angeles)
     */
    private String fullAreaName;
    /**
     * 邮编配置 JSON格式存储邮编范围或列表
     */
    private String postalCodes;

    /**
     * 限制类型 (FORBIDDEN-禁止配送, REMOTE_FEE-偏远地区费, NORMAL-正常)
     */
    private String restrictionType;


    /**
     * 附加费公式 (如：3*weight_kg,min:48 表示每公斤3元，最低48元)
     */
    private String feeFormula;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态
     */
    private Integer status;

}