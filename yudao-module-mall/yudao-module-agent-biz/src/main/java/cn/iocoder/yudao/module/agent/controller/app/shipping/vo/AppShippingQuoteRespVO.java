package cn.iocoder.yudao.module.agent.controller.app.shipping.vo;

import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户端 - 运费查询 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户端 - 运费查询 Response VO")
@Data
public class AppShippingQuoteRespVO extends ShippingQuoteRespBO {

//    @Schema(description = "物流产品ID", example = "1920529007397355522")
//    private Long id;
//
//    @Schema(description = "价格ID", example = "1920529007397355522")
//    private Long priceId;
//
//    @Schema(description = "分区编号", example = "US")
//    private String zoneCode;
//
//    @Schema(description = "物流产品名称", example = "美国专线小包p")
//    private String name;
//
//    @Schema(description = "图标URL", example = "https://img1.cnfans.com/xxx.png")
//    private String iconUrl;
//
//    @Schema(description = "产品特色描述", example = "1、该线路为三角运输...")
//    private String features;
//
//    @Schema(description = "运输时效", example = "12-20")
//    private String transitTime;
//
//    @Schema(description = "是否包税", example = "true")
//    private Boolean taxInclude;
//
//    @Schema(description = "是否可用", example = "true")
//    private Boolean available;
//
//    @Schema(description = "不可用原因", example = "超重")
//    private String unavailableReason;
//
//    @Schema(description = "排序", example = "15")
//    private Integer sort;
//
//    @Schema(description = "最小申报价值(分)", example = "500")
//    private Integer minDeclareValue;
//
//    @Schema(description = "最大申报价值(分)", example = "12000")
//    private Integer maxDeclareValue;
//
//    @Schema(description = "默认申报类型", example = "Weight")
//    private String defaultDeclareType;
//
//    @Schema(description = "每公斤申报价值(分)", example = "1200")
//    private Integer declarePerKg;
//
//    @Schema(description = "申报比例", example = "0.2")
//    private String declareRatio;
//
//    @Schema(description = "是否启用IOSS", example = "false")
//    private Boolean iossEnabled;
//
//    @Schema(description = "是否免费保险", example = "false")
//    private Boolean freeInsure;
//
//    @Schema(description = "妥投率", example = "98.1")
//    private BigDecimal deliveryRate;
//
//    @Schema(description = "折扣费率", example = "0.1")
//    private BigDecimal discountRate;
//
//    @Schema(description = "关税税率", example = "0")
//    private BigDecimal tariffRate;
//
//    @Schema(description = "是否预收关税", example = "false")
//    private Boolean prepayTariff;
//
//    @Schema(description = "服务等级", example = "Standard")
//    private String serviceLevel;
//
//    @Schema(description = "是否可带电", example = "false")
//    private Boolean electronic;
//
//    @Schema(description = "是否化妆品专线", example = "false")
//    private Boolean cosmetic;
//
//    @Schema(description = "是否服装专线", example = "false")
//    private Boolean clothing;
//
//    @Schema(description = "是否液体专线", example = "false")
//    private Boolean liquid;
//
//    @Schema(description = "是否大货专线", example = "false")
//    private Boolean large;
//
//    @Schema(description = "是否推荐", example = "false")
//    private Boolean recommended;
//
//
//    /**
//     * 费用详情
//     */
//    @Schema(description = "重量(g)", example = "600")
//    private Integer weight;
//
//    @Schema(description = "长度(cm)", example = "30")
//    private BigDecimal length;
//
//    @Schema(description = "宽度(cm)", example = "30")
//    private BigDecimal width;
//
//    @Schema(description = "高度(cm)", example = "3")
//    private BigDecimal height;
//
//    @Schema(description = "体积重(g)", example = "338")
//    private Integer volumeWeight;
//
//    @Schema(description = "计费重量(g)", example = "700")
//    private Integer chargeableWeight;
//
//    @Schema(description = "总费用", example = "25.23")
//    private Integer total;
//
//    @Schema(description = "运费", example = "21.27")
//    private Integer freight;
//
//    @Schema(description = "清关费", example = "0.00")
//    private Integer customsFee;
//
//    @Schema(description = "燃油费", example = "0.00")
//    private Integer fuelFee;
//
//    @Schema(description = "挂号费", example = "0.00")
//    private Integer registrationFee;
//
//    @Schema(description = "操作费", example = "2.37")
//    private Integer operationFee;
//
//    @Schema(description = "服务费", example = "1.58")
//    private Integer serviceFee;
//
//    @Schema(description = "首重费用", example = "16.62")
//    private Integer feeFirst;
//
//    @Schema(description = "续重费用", example = "4.65")
//    private Integer feeContinue;
//
//    @Schema(description = "附加费", example = "null")
//    private Integer additionalFee;
//
//    @Schema(description = "首重重量(g)", example = "500")
//    private Integer weightFirst;
//
//    @Schema(description = "续重重量(g)", example = "200")
//    private Integer weightContinue;
//
//    @Schema(description = "是否需要体积计算", example = "true")
//    private Boolean needVolumeCal;
//
//    @Schema(description = "体积重基数", example = "8000")
//    private Integer volumeBase;
//
//    /**
//     * 限制信息
//     */
//
//    @Schema(description = "最小重量(g)", example = "0")
//    private Integer minWeight;
//
//    @Schema(description = "最大重量(g)", example = "20000")
//    private Integer maxWeight;
//
//    @Schema(description = "尺寸限制", example = "长<=100cm，宽<=65cm，长+2*(宽+高)<=280cm")
//    private String sizeRestrictions;
//
//    @Schema(description = "尺寸限制描述", example = "长<=100cm，宽<=65cm，长+2*(宽+高)<=280cm")
//    private String dimensionRestriction;
//
//    @Schema(description = "体积重规则描述", example = "包裹将会被计算体积重...")
//    private String volumeWeightRule;
//
//    @Schema(description = "分类限制配置(JSON格式)", example = "[{\"id\":25,\"allowList\":[],\"blockList\":[198,197,196]}]")
//    private String categoryRestrictions;


}
