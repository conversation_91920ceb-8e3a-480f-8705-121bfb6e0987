package cn.iocoder.yudao.module.agent.controller.app.shipping;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteReqVO;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.service.shipping.ShippingQuoteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 用户端 - 运费计算控制器
 *
 * <AUTHOR>
 */
@Tag(name = "用户端 - 运费计算")
@RestController
@RequestMapping("/agent/shipping-calculation")
@Validated
@Slf4j
public class AppShippingController {

    @Resource
    private ShippingQuoteService shippingQuoteService;

    @PostMapping("/quote")
    @Operation(summary = "运费查询")
    @PermitAll
    public CommonResult<List<AppShippingQuoteRespVO>> getShippingQuotes(@Valid @RequestBody AppShippingQuoteReqVO reqVO) {
        log.info("=== 用户端运费查询 ===");
        log.info("国家: {}, 重量: {}g, 分类: {}", reqVO.getCountryCode(), reqVO.getWeight(), reqVO.getCategoryIds());

        // 调用服务层进行运费查询
        List<AppShippingQuoteRespVO> quotes = shippingQuoteService.getAppShippingQuotes(reqVO);
        
        log.info("用户端运费查询完成: 找到{}个方案", quotes.size());
        return success(quotes);
    }

    @GetMapping("/get-detail")
    @Operation(summary = "运费详情")
    @PermitAll
    public CommonResult<AppShippingQuoteRespVO> getShippingQuoteDetail(@RequestParam("id") Long id) {
        log.info("=== 用户端运费详情 ===");
        log.info("方案ID: {}", id);


        // 调用服务层进行运费查询
        AppShippingQuoteRespVO quote = shippingQuoteService.getAppShippingQuoteDetail(id);

        log.info("用户端运费详情完成: {}", quote);
        return success(quote);
    }

}
