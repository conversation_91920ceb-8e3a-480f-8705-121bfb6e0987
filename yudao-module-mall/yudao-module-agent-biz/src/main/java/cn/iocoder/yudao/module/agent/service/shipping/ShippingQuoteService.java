package cn.iocoder.yudao.module.agent.service.shipping;

import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteReqVO;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.LogisticsPlanBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;

import java.util.List;

/**
 * 运费查询服务接口
 *
 * <AUTHOR>
 */
public interface ShippingQuoteService {


    /**
     * 查询运费报价（用户端）
     * 注意：这个方法直接返回VO，用于Controller层调用
     *
     * @param reqVO 查询请求VO
     * @return 运费报价VO列表
     */
    List<AppShippingQuoteRespVO> getAppShippingQuotes(AppShippingQuoteReqVO reqVO);

    /**
     * 获取可用的运费方案列表（用于价格计算器）
     *
     * @param reqBO 查询请求BO
     * @return 物流方案BO列表
     */
    List<LogisticsPlanBO> getEnableShippingPlans(ShippingCalculationReqBO reqBO);

    /**
     * 查询运费报价（Service层内部使用）
     *
     * @param reqBO 查询请求BO
     * @return 运费报价BO列表
     */
    List<ShippingQuoteRespBO> getShippingQuotes(ShippingCalculationReqBO reqBO);

    /**
     * 获取运费方案详情（用户端）
     *
     * @param id 运费方案编号
     * @return 运费方案详情VO
     */
    AppShippingQuoteRespVO getAppShippingQuoteDetail(Long id);
}
