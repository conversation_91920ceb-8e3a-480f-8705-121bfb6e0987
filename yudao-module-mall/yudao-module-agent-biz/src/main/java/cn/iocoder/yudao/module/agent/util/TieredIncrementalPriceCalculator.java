package cn.iocoder.yudao.module.agent.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 阶梯递增价格计算工具类
 * 用于处理"先分阶梯，阶梯内递增"的复杂计费模式
 *
 * <AUTHOR>
 */
@Slf4j
public class TieredIncrementalPriceCalculator {

    /**
     * 阶梯递增价格配置项
     */
    @Data
    public static class TieredIncrementalItem {
        /**
         * 阶梯开始重量(g)
         */
        private Integer tierStart;

        /**
         * 阶梯结束重量(g)
         */
        private Integer tierEnd;

        /**
         * 首重重量(g)
         */
        private Integer firstWeight;

        /**
         * 首重价格(分)
         */
        private Integer firstPrice;

        /**
         * 续重单位(g)
         */
        private Integer additionalWeight;

        /**
         * 续重单价(分)
         */
        private Integer additionalPrice;

        /**
         * 挂号费(分)
         */
        private Integer registrationFee;

        /**
         * 进位制(g) - 重量进位单位，如500表示按500g进位
         */
        private Integer roundingUnit;

        /**
         * 最低计费重(g) - 该阶梯的最低计费重量
         */
        private Integer minChargeWeight;
    }

    /**
     * 阶梯递增价格计算结果
     */
    @Data
    public static class TieredIncrementalResult {
        /**
         * 基础运费(分)
         */
        private BigDecimal baseFee;
        
        /**
         * 挂号费(分)
         */
        private BigDecimal registrationFee;
        
        /**
         * 是否使用阶梯配置中的挂号费
         */
        private boolean useTieredRegistrationFee;
        
        /**
         * 匹配的阶梯配置
         */
        private TieredIncrementalItem matchedTier;

        public TieredIncrementalResult() {
            this.baseFee = BigDecimal.ZERO;
            this.registrationFee = BigDecimal.ZERO;
            this.useTieredRegistrationFee = false;
        }
    }

    /**
     * 计算阶梯递增价格
     * 
     * @param priceConfigJson 阶梯递增价格配置JSON
     * @param weightGrams 重量(g)
     * @param defaultRegistrationFee 默认挂号费(分)
     * @return 计算结果
     */
    public static TieredIncrementalResult calculateTieredIncrementalPrice(String priceConfigJson,
                                                                         Integer weightGrams, 
                                                                         Integer defaultRegistrationFee) {
        log.info("=== 开始阶梯递增价格计算 ===");
        log.info("输入参数: 重量={}g, 默认挂号费={}分", weightGrams, defaultRegistrationFee);
        log.info("阶梯递增价格配置: {}", priceConfigJson);
        
        TieredIncrementalResult result = new TieredIncrementalResult();
        result.setRegistrationFee(defaultRegistrationFee != null ? 
                                 new BigDecimal(defaultRegistrationFee) : BigDecimal.ZERO);

        if (StrUtil.isBlank(priceConfigJson) || weightGrams == null || weightGrams <= 0) {
            log.warn("阶梯递增价格配置为空或重量无效，返回默认结果");
            return result;
        }

        try {
            // 解析阶梯递增价格配置
            List<TieredIncrementalItem> tieredItems = parseTieredIncrementalPrices(priceConfigJson);
            log.info("解析到{}个阶梯递增配置", tieredItems.size());
            
            for (int i = 0; i < tieredItems.size(); i++) {
                TieredIncrementalItem item = tieredItems.get(i);
                log.info("阶梯{}: 重量范围=[{}-{}]g, 首重={}g/{}分, 续重={}g/{}分, 挂号费={}分", 
                        i+1, item.getTierStart(), item.getTierEnd(), 
                        item.getFirstWeight(), item.getFirstPrice(),
                        item.getAdditionalWeight(), item.getAdditionalPrice(),
                        item.getRegistrationFee());
            }

            // 查找匹配的阶梯
            TieredIncrementalItem matchedTier = findMatchingTier(tieredItems, weightGrams);
            if (matchedTier == null) {
                log.warn("未找到匹配的阶梯递增配置，重量: {}g", weightGrams);
                return result;
            }

            log.info("匹配到阶梯: 重量范围=[{}-{}]g, 首重={}g/{}分, 续重={}g/{}分", 
                    matchedTier.getTierStart(), matchedTier.getTierEnd(),
                    matchedTier.getFirstWeight(), matchedTier.getFirstPrice(),
                    matchedTier.getAdditionalWeight(), matchedTier.getAdditionalPrice());
            result.setMatchedTier(matchedTier);

            // 计算基础运费
            BigDecimal baseFee = calculateIncrementalFee(matchedTier, weightGrams);
            result.setBaseFee(baseFee);

            // 处理挂号费覆盖
            if (matchedTier.getRegistrationFee() != null) {
                result.setRegistrationFee(new BigDecimal(matchedTier.getRegistrationFee()));
                result.setUseTieredRegistrationFee(true);
                log.info("使用阶梯配置中的挂号费: {}分", matchedTier.getRegistrationFee());
            } else {
                log.info("使用默认挂号费: {}分", result.getRegistrationFee());
            }

            log.info("=== 阶梯递增价格计算完成 ===");
            log.info("最终结果: 基础运费={}分, 挂号费={}分, 使用阶梯挂号费={}", 
                     result.getBaseFee(), result.getRegistrationFee(), result.isUseTieredRegistrationFee());

        } catch (Exception e) {
            log.error("阶梯递增价格计算失败: tieredIncrementalPrices={}, weight={}g", 
                     priceConfigJson, weightGrams, e);
        }

        return result;
    }

    /**
     * 解析阶梯递增价格配置
     */
    private static List<TieredIncrementalItem> parseTieredIncrementalPrices(String priceConfigJson) {
        List<TieredIncrementalItem> items = new ArrayList<>();

        try {
            JSONArray jsonArray = JSONUtil.parseArray(priceConfigJson);
            
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                TieredIncrementalItem item = new TieredIncrementalItem();
                item.setTierStart(jsonObject.getInt("tierStart"));
                item.setTierEnd(jsonObject.getInt("tierEnd"));
                item.setFirstWeight(jsonObject.getInt("firstWeight"));
                item.setFirstPrice(jsonObject.getInt("firstPrice"));
                item.setAdditionalWeight(jsonObject.getInt("additionalWeight"));
                item.setAdditionalPrice(jsonObject.getInt("additionalPrice"));
                item.setRegistrationFee(jsonObject.getInt("registrationFee"));
                
                items.add(item);
            }
            
        } catch (Exception e) {
            log.error("解析阶梯递增价格配置失败: {}", priceConfigJson, e);
            throw new RuntimeException("阶梯递增价格配置格式错误", e);
        }

        return items;
    }

    /**
     * 查找匹配的阶梯
     */
    private static TieredIncrementalItem findMatchingTier(List<TieredIncrementalItem> items, Integer weightGrams) {
        for (TieredIncrementalItem item : items) {
            if (weightGrams >= item.getTierStart() && weightGrams <= item.getTierEnd()) {
                return item;
            }
        }
        return null;
    }

    /**
     * 计算阶梯内的递增费用
     */
    private static BigDecimal calculateIncrementalFee(TieredIncrementalItem item, Integer weightGrams) {
        log.info("--- 阶梯内递增费用计算 ---");

        if (item.getFirstWeight() == null || item.getFirstPrice() == null) {
            log.warn("首重配置不完整: 首重重量={}, 首重价格={}",
                    item.getFirstWeight(), item.getFirstPrice());
            return BigDecimal.ZERO;
        }

        // 1. 处理最低计费重
        Integer actualWeight = weightGrams;
        if (item.getMinChargeWeight() != null && weightGrams < item.getMinChargeWeight()) {
            actualWeight = item.getMinChargeWeight();
            log.info("应用最低计费重: 实际重量={}g, 最低计费重={}g, 计费重量={}g",
                    weightGrams, item.getMinChargeWeight(), actualWeight);
        }

        // 2. 处理进位制
        if (item.getRoundingUnit() != null && item.getRoundingUnit() > 0) {
            Integer roundingUnit = item.getRoundingUnit();
            Integer roundedWeight = ((actualWeight + roundingUnit - 1) / roundingUnit) * roundingUnit;
            log.info("应用进位制: 计费重量={}g, 进位单位={}g, 进位后重量={}g",
                    actualWeight, roundingUnit, roundedWeight);
            actualWeight = roundedWeight;
        }

        // 3. 首重费用
        BigDecimal totalFee = new BigDecimal(item.getFirstPrice());
        log.info("首重费用: {}g = {}分", item.getFirstWeight(), totalFee);

        // 4. 计算续重费用
        if (actualWeight > item.getFirstWeight() &&
            item.getAdditionalWeight() != null &&
            item.getAdditionalPrice() != null) {

            int additionalWeight = actualWeight - item.getFirstWeight();
            int additionalUnits = (int) Math.ceil((double) additionalWeight / item.getAdditionalWeight());
            BigDecimal additionalFee = new BigDecimal(item.getAdditionalPrice()).multiply(new BigDecimal(additionalUnits));

            log.info("续重计算: 超出重量={}g, 续重单位={}g, 续重次数={}, 续重单价={}分",
                    additionalWeight, item.getAdditionalWeight(), additionalUnits, item.getAdditionalPrice());
            log.info("续重费用: {} × {} = {}分", item.getAdditionalPrice(), additionalUnits, additionalFee);

            totalFee = totalFee.add(additionalFee);
        } else {
            log.info("重量{}g未超过首重{}g，无续重费用", actualWeight, item.getFirstWeight());
        }

        log.info("阶梯内递增总费用: {}分", totalFee);
        log.info("--- 阶梯内递增费用计算完成 ---");
        return totalFee;
    }
}
