package cn.iocoder.yudao.module.agent.dal.mysql.country;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.agent.dal.dataobject.country.AgentCountryDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.agent.controller.admin.country.vo.*;

import java.util.List;

/**
 * 代购服务国家 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AgentCountryMapper extends BaseMapperX<AgentCountryDO> {

    default PageResult<AgentCountryDO> selectPage(CountryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AgentCountryDO>()
                .eqIfPresent(AgentCountryDO::getCode, reqVO.getCode())
                .eqIfPresent(AgentCountryDO::getNameZh, reqVO.getNameZh())
                .eqIfPresent(AgentCountryDO::getNameEn, reqVO.getNameEn())
                .eqIfPresent(AgentCountryDO::getNameFr, reqVO.getNameFr())
                .eqIfPresent(AgentCountryDO::getNameDe, reqVO.getNameDe())
                .eqIfPresent(AgentCountryDO::getNameEs, reqVO.getNameEs())
                .eqIfPresent(AgentCountryDO::getNameAr, reqVO.getNameAr())
                .eqIfPresent(AgentCountryDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AgentCountryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AgentCountryDO::getId));
    }

    default List<AgentCountryDO> selectListByStatus(Integer status){
        return selectList(AgentCountryDO::getStatus, status);
    }
}