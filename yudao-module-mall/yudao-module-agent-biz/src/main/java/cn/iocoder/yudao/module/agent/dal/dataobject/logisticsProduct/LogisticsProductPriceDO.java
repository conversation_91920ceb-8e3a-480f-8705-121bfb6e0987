package cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代购物流产品价格规则 DO
 *
 * <AUTHOR>
 */
@TableName("agent_logistics_product_price")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsProductPriceDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 产品编号
     */
    private Long productId;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 分区编码;为空表示全国统一价格
     */
    private String zoneCode;
    /**
     * 时效
     */
    private String transitTime;
    /**
     * 时效说明
     */
    private String timelinessInfo;
    /**
     * 计费方式;
     * WEIGHT(重量), VOLUME(体积), PIECE(件数)
     */
    private String chargeType;

    /**
     * 体积重计算基数; 一般为8000
     */
    private Integer volumeBase;

    /**
     * 重量比较方式;
     * 一般为包裹实际重量和体积重量相比，取较大者，也有情况是体积重低于实际重量2倍的，按照实际重量收费;达到或超过实际重量2倍的，按照体积重量收取。
     * todo 需要制定一个比较的逻辑配置或者公式
     */
    private String weightCompareType;

    /**
     * 阶梯计费还是固定递增;
     * TIERED(阶梯), INCREMENTAL(递增)，TIERED_INCREMENTAL(混合)
     */
    private String priceType;
    /**
     * 首重(g)/首件数量
     */
    private Integer firstUnit;
    /**
     * 首重/首件价格(分)
     */
    private Integer firstPrice;
    /**
     * 续重(g)/续件单位
     */
    private Integer additionalUnit;
    /**
     * 续重/续件价格(分)
     */
    private Integer additionalPrice;
    /**
     * 最小重量(g)
     */
    private Integer minWeight;
    /**
     * 最大重量(g)
     */
    private Integer maxWeight;
    /**
     * 价格配置(JSON格式)
     * 根据priceType字段区分使用方式：
     * - TIERED: 阶梯价格配置，格式为TieredPriceItem数组
     * - TIERED_INCREMENTAL: 阶梯递增价格配置，格式为TieredIncrementalItem数组
     * - INCREMENTAL: 不使用此字段，使用firstUnit/firstPrice/additionalUnit/additionalPrice
     */
    private String priceConfig;

    /**
     * 燃油费率
     */
    private BigDecimal fuelFeeRate;
    /**
     * 挂号费(分)
     */
    private Integer registrationFee;
    /**
     * 操作费(分)
     */
    private Integer operationFee;
    /**
     * 服务费(分)
     */
    private Integer serviceFee;
    /**
     * 清关费(分)
     */
    private Integer customsFee;
    /**
     * 是否预收关税
     */
    private Boolean prepayTariff;
    /**
     * 最小申报价值(分)
     */
    private Integer minDeclareValue;
    /**
     * 最大申报价值(分)
     */
    private Integer maxDeclareValue;

    /**
     * 妥投率
     */
    private BigDecimal deliveryRate;

    /**
     * 关税税率
     */
    private BigDecimal tariffRate;
    /**
     * 生效时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime effectiveTime;
    /**
     * 失效时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime expireTime;
    /**
     * 对应类 cn.iocoder.yudao.module.agent.util.SizeLimitRule
     * 尺寸限制配置 JSON格式
     * 格式：{"maxLength":120,"maxWidth":60,"maxHeight":60,"maxGirth":300,"maxSingleSide":120}
     * 单位：cm，针对该国家/分区的特殊尺寸限制
     */
    private String sizeRestrictions;

    /**
     * 折扣费率，用于运费利润调节
     * 例如：1.5表示在计算出的运费基础上乘以1.5倍
     * 默认值为1.0（不调节）
     */
    private BigDecimal discountRate;

    /**
     * 备注
     */
    private String memo;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态 0启用  1停用
     */
    private Integer status;

}