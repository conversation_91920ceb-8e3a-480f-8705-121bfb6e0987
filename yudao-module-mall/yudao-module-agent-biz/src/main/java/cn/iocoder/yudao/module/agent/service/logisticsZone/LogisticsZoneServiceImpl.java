package cn.iocoder.yudao.module.agent.service.logisticsZone;

import cn.hutool.json.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsZone.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.module.agent.util.PostalCodeUtil;
import cn.iocoder.yudao.module.agent.util.AreaMatchingUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.agent.dal.mysql.logisticsZone.LogisticsZoneMapper;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代购物流国家分区 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class LogisticsZoneServiceImpl implements LogisticsZoneService {

    /**
     * 默认需要分区的物流国家
     */
    private static final List<String> zoneCountries = Collections.unmodifiableList(Arrays.asList("AU", "AR"));

    @Resource
    private LogisticsZoneMapper logisticsZoneMapper;

    @Override
    public Long createLogisticsZone(LogisticsZoneSaveReqVO createReqVO) {
        // 插入
        LogisticsZoneDO logisticsZone = BeanUtils.toBean(createReqVO, LogisticsZoneDO.class);
        logisticsZoneMapper.insert(logisticsZone);
        // 返回
        return logisticsZone.getId();
    }

    @Override
    public void updateLogisticsZone(LogisticsZoneSaveReqVO updateReqVO) {
        // 校验存在
        validateLogisticsZoneExists(updateReqVO.getId());
        // 更新
        LogisticsZoneDO updateObj = BeanUtils.toBean(updateReqVO, LogisticsZoneDO.class);
        logisticsZoneMapper.updateById(updateObj);
    }

    @Override
    public void deleteLogisticsZone(Long id) {
        // 校验存在
        validateLogisticsZoneExists(id);
        // 删除
        logisticsZoneMapper.deleteById(id);
    }

    private void validateLogisticsZoneExists(Long id) {
        if (logisticsZoneMapper.selectById(id) == null) {
            throw exception(LOGISTICS_ZONE_NOT_EXISTS);
        }
    }

    @Override
    public LogisticsZoneDO getLogisticsZone(Long id) {
        return logisticsZoneMapper.selectById(id);
    }

    @Override
    public PageResult<LogisticsZoneDO> getLogisticsZonePage(LogisticsZonePageReqVO pageReqVO) {
        return logisticsZoneMapper.selectPage(pageReqVO);
    }

    @Override
    public LogisticsZoneDO findZoneByPostalCode(String countryCode, Long productId, String postalCode) {
        List<LogisticsZoneDO> zones = logisticsZoneMapper.selectListByCountryAndProduct(countryCode, productId);

        for (LogisticsZoneDO zone : zones) {
            if (PostalCodeUtil.isPostalCodeMatch(postalCode, zone.getPostalCodes())) {
                return zone;
            }
        }

        return null;
    }

    @Override
    public List<LogisticsZoneDO> getZoneListByCountryAndProduct(String countryCode, Long productId) {
        return logisticsZoneMapper.selectListByCountryAndProduct(countryCode, productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LogisticsZoneImportRespVO importLogisticsZoneList(Long productId, List<LogisticsZoneImportExcelVO> importZones, boolean isUpdateSupport) {
        if (CollUtil.isEmpty(importZones)) {
            throw exception(LOGISTICS_ZONE_IMPORT_LIST_IS_EMPTY);
        }

        log.info("开始导入物流分区数据，产品ID: {}, 数据总数: {}", productId, importZones.size());

        LogisticsZoneImportRespVO respBuilder = LogisticsZoneImportRespVO.builder()
                .createSuccessCount(0).updateSuccessCount(0).failureCount(0)
                .failureData(new ArrayList<>()).build();

        int createSuccessCount = 0;
        int updateSuccessCount = 0;
        int failureCount = 0;
        List<Map<String, Object>> failureData = new ArrayList<>();

        for (int i = 0; i < importZones.size(); i++) {
            LogisticsZoneImportExcelVO importZone = importZones.get(i);
            try {
                // 数据清理和预处理
                cleanImportZoneData(importZone);

                // 数据验证
                String validationError = validateImportZone(importZone);
                if (StrUtil.isNotBlank(validationError)) {
                    failureCount++;
                    Map<String, Object> failureRow = new HashMap<>();
                    failureRow.put("row", i + 2); // Excel 行号从2开始（第1行是标题）
                    failureRow.put("data", importZone);
                    failureRow.put("reason", validationError);
                    failureData.add(failureRow);
                    continue;
                }

                // 检查是否已存在
                LogisticsZoneDO existingZone = findExistingZone(productId, importZone);

                if (existingZone != null) {
                    if (!isUpdateSupport) {
                        failureCount++;
                        Map<String, Object> failureRow = new HashMap<>();
                        failureRow.put("row", i + 2);
                        failureRow.put("data", importZone);
                        failureRow.put("reason", "分区已存在，且不允许更新");
                        failureData.add(failureRow);
                        continue;
                    }

                    // 更新现有分区
                    LogisticsZoneSaveReqVO updateReqVO = convertToSaveReqVO(importZone, productId);
                    updateReqVO.setId(existingZone.getId());
                    updateLogisticsZone(updateReqVO);
                    updateSuccessCount++;
                } else {
                    // 创建新分区
                    createLogisticsZone(convertToSaveReqVO(importZone, productId));
                    createSuccessCount++;
                }
            } catch (Exception e) {
                failureCount++;
                Map<String, Object> failureRow = new HashMap<>();
                failureRow.put("row", i + 2);
                failureRow.put("data", importZone);
                failureRow.put("reason", "处理失败: " + e.getMessage());
                failureData.add(failureRow);
            }
        }

        return LogisticsZoneImportRespVO.builder()
                .createSuccessCount(createSuccessCount)
                .updateSuccessCount(updateSuccessCount)
                .failureCount(failureCount)
                .failureData(failureData)
                .build();
    }

    @Override
    public String getZoneCodeByPostalCode(String countryCode, String postalCode) {
        //根据国家和邮编获取分区编号（不需要分区则返回null,国家存在多个分区且未传递邮编则返回默认第一个分区）
        //判断是否在需要分区的国家列表内
        if(!zoneCountries.contains(countryCode)){
            return null;
        }

        if(StrUtil.isBlank(postalCode)){
            LogisticsZoneDO zones = logisticsZoneMapper.selectOneByCountryAndPostalCode(countryCode, null);

        }





        log.info("根据邮编获取分区编码: 国家={}, 邮编={}", countryCode, postalCode);

        // 判断是否在需要分区的国家列表内
        if (!zoneCountries.contains(countryCode)) {
            log.debug("国家{}不需要分区处理", countryCode);
            return null;
        }

        // 如果没有邮编，返回该国家的默认分区（第一个正常分区）
        if (StrUtil.isBlank(postalCode)) {
            LogisticsZoneDO defaultZone = getDefaultZoneByCountry(countryCode);
            return defaultZone != null ? defaultZone.getZoneCode() : null;
        }

        // 根据邮编查找匹配的分区
        List<LogisticsZoneDO> zones = logisticsZoneMapper.selectListByCountryAndProduct(countryCode, null);
        for (LogisticsZoneDO zone : zones) {
            if (PostalCodeUtil.isPostalCodeMatch(postalCode, zone.getPostalCodes())) {
                log.info("找到匹配分区: 国家={}, 邮编={}, 分区={}", countryCode, postalCode, zone.getZoneCode());
                return zone.getZoneCode();
            }
        }

        log.warn("未找到匹配的分区: 国家={}, 邮编={}", countryCode, postalCode);
        return null;
    }
    /**
     * 获取指定国家的默认分区（第一个正常分区）
     */
    private LogisticsZoneDO getDefaultZoneByCountry(String countryCode) {
        List<LogisticsZoneDO> zones = logisticsZoneMapper.selectList(
                new LambdaQueryWrapperX<LogisticsZoneDO>()
                        .eq(LogisticsZoneDO::getCountryCode, countryCode)
                        .eq(LogisticsZoneDO::getStatus, 1) // 启用状态
                        .ne(LogisticsZoneDO::getRestrictionType, "FORBIDDEN") // 排除禁止配送区域
                        .orderByAsc(LogisticsZoneDO::getSort)
                        .orderByAsc(LogisticsZoneDO::getCreateTime)
                        .last("LIMIT 1")
        );

        return CollUtil.isNotEmpty(zones) ? zones.get(0) : null;
    }

    /**
     * 验证导入的分区数据
     */
    private String validateImportZone(LogisticsZoneImportExcelVO importZone) {
        if (StrUtil.isBlank(importZone.getCountryCode())) {
            return "国家编码不能为空";
        }

        // 验证国家编码格式（应该是2位大写字母）
        String countryCode = importZone.getCountryCode().trim();
        if (!countryCode.matches("^[A-Z]{2}$")) {
            return "国家编码格式不正确，应为2位大写字母（如：US、AU、FR）";
        }

        if (StrUtil.isBlank(importZone.getZoneCode())) {
            return "分区编码不能为空";
        }
        if (StrUtil.isBlank(importZone.getZoneName())) {
            return "分区名称不能为空";
        }
        if (importZone.getStatus() == null) {
            return "状态不能为空";
        }
        if (importZone.getStatus() != 0 && importZone.getStatus() != 1) {
            return "状态值必须为0或1";
        }

        // 验证邮编配置格式
        if (StrUtil.isNotBlank(importZone.getPostalCodes())) {
            if (!PostalCodeUtil.isValidPostalCodesConfig(importZone.getPostalCodes())) {
                return "邮编配置格式不正确，应为JSON数组格式（如：[\"12345\"]或[\"12000-12999\"]）";
            }
        }

        // 验证限制类型
        if (StrUtil.isNotBlank(importZone.getRestrictionType())) {
            if (!"NORMAL".equals(importZone.getRestrictionType()) &&
                !"REMOTE_FEE".equals(importZone.getRestrictionType()) &&
                !"FORBIDDEN".equals(importZone.getRestrictionType())) {
                return "限制类型必须为NORMAL、REMOTE_FEE或FORBIDDEN";
            }
        }

        return null;
    }

    /**
     * 查找已存在的分区
     */
    private LogisticsZoneDO findExistingZone(Long productId, LogisticsZoneImportExcelVO importZone) {

        return  logisticsZoneMapper.findExistingZone(productId, importZone.getCountryCode(), importZone.getZoneCode());

    }

    /**
     * 转换为保存请求VO
     */
    private LogisticsZoneSaveReqVO convertToSaveReqVO(LogisticsZoneImportExcelVO importZone, Long productId) {
        LogisticsZoneSaveReqVO saveReqVO = new LogisticsZoneSaveReqVO();
        saveReqVO.setCountryCode(importZone.getCountryCode());
        // 设置产品ID列表，如果导入时指定了产品ID，则创建包含该产品ID的JSON数组
        if (productId != null) {
            saveReqVO.setProductIds("[" + productId + "]");
        }
        saveReqVO.setZoneCode(importZone.getZoneCode());
        saveReqVO.setZoneName(importZone.getZoneName());
        saveReqVO.setStateProvince(importZone.getStateProvince());
        saveReqVO.setCity(importZone.getCity());
        saveReqVO.setDistrict(importZone.getDistrict());
        saveReqVO.setSpecialAreaType(importZone.getSpecialAreaType());

        // 生成完整区域名称
        if (StrUtil.isBlank(importZone.getFullAreaName())) {
            saveReqVO.setFullAreaName(AreaMatchingUtil.generateFullAreaName(
                importZone.getStateProvince(), importZone.getCity(),
                importZone.getDistrict(), importZone.getSpecialAreaType()));
        } else {
            saveReqVO.setFullAreaName(importZone.getFullAreaName());
        }

        saveReqVO.setPostalCodes(importZone.getPostalCodes());
        saveReqVO.setRestrictionType(StrUtil.isBlank(importZone.getRestrictionType()) ? "NORMAL" : importZone.getRestrictionType());
        saveReqVO.setFeeFormula(importZone.getFeeFormula());
        saveReqVO.setRemark(importZone.getRemark());
        saveReqVO.setSort(importZone.getSort() != null ? importZone.getSort() : 0);
        saveReqVO.setStatus(importZone.getStatus());

        return saveReqVO;
    }

    /**
     * 格式化分区数据用于日志打印
     */
    private String formatZoneForLog(LogisticsZoneImportExcelVO zone) {
        return String.format("countryCode='%s', zoneCode='%s', zoneName='%s', stateProvince='%s', city='%s', district='%s', specialAreaType='%s', fullAreaName='%s', postalCodes='%s', restrictionType='%s', feeFormula='%s', remark='%s', sort=%s, status=%s",
                safeString(zone.getCountryCode()), safeString(zone.getZoneCode()), safeString(zone.getZoneName()),
                safeString(zone.getStateProvince()), safeString(zone.getCity()), safeString(zone.getDistrict()),
                safeString(zone.getSpecialAreaType()), safeString(zone.getFullAreaName()), safeString(zone.getPostalCodes()),
                safeString(zone.getRestrictionType()), safeString(zone.getFeeFormula()), safeString(zone.getRemark()),
                zone.getSort(), zone.getStatus());
    }

    /**
     * 安全获取字符串，避免null值
     */
    private String safeString(String str) {
        return str == null ? "null" : str;
    }

    /**
     * 清理和预处理导入的分区数据
     */
    private void cleanImportZoneData(LogisticsZoneImportExcelVO importZone) {
        // 清理国家编码：去除空格并转为大写
        if (StrUtil.isNotBlank(importZone.getCountryCode())) {
            importZone.setCountryCode(importZone.getCountryCode().trim().toUpperCase());
        }

        // 清理分区编码：去除空格并转为大写
        if (StrUtil.isNotBlank(importZone.getZoneCode())) {
            importZone.setZoneCode(importZone.getZoneCode().trim().toUpperCase());
        }

        // 清理分区名称：去除前后空格
        if (StrUtil.isNotBlank(importZone.getZoneName())) {
            importZone.setZoneName(importZone.getZoneName().trim());
        }

        // 清理限制类型：去除空格并转为大写
        if (StrUtil.isNotBlank(importZone.getRestrictionType())) {
            importZone.setRestrictionType(importZone.getRestrictionType().trim().toUpperCase());
        }

        // 清理特殊区域类型：去除空格并转为大写
        if (StrUtil.isNotBlank(importZone.getSpecialAreaType())) {
            importZone.setSpecialAreaType(importZone.getSpecialAreaType().trim().toUpperCase());
        }

        // 清理其他字符串字段的空格
        if (StrUtil.isNotBlank(importZone.getStateProvince())) {
            importZone.setStateProvince(importZone.getStateProvince().trim());
        }
        if (StrUtil.isNotBlank(importZone.getCity())) {
            importZone.setCity(importZone.getCity().trim());
        }
        if (StrUtil.isNotBlank(importZone.getDistrict())) {
            importZone.setDistrict(importZone.getDistrict().trim());
        }
        if (StrUtil.isNotBlank(importZone.getFullAreaName())) {
            importZone.setFullAreaName(importZone.getFullAreaName().trim());
        }
        if (StrUtil.isNotBlank(importZone.getPostalCodes())) {
            importZone.setPostalCodes(importZone.getPostalCodes().trim());
        }
        if (StrUtil.isNotBlank(importZone.getFeeFormula())) {
            importZone.setFeeFormula(importZone.getFeeFormula().trim());
        }
        if (StrUtil.isNotBlank(importZone.getRemark())) {
            importZone.setRemark(importZone.getRemark().trim());
        }


    }

}