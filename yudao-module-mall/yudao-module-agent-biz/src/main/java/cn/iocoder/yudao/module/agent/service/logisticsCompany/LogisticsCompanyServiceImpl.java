package cn.iocoder.yudao.module.agent.service.logisticsCompany;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.agent.controller.admin.logisticsCompany.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsCompany.LogisticsCompanyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.agent.dal.mysql.logisticsCompany.LogisticsCompanyMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代购物流公司 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LogisticsCompanyServiceImpl implements LogisticsCompanyService {

    @Resource
    private LogisticsCompanyMapper logisticsCompanyMapper;

    @Override
    public Long createLogisticsCompany(LogisticsCompanySaveReqVO createReqVO) {
        // 插入
        LogisticsCompanyDO logisticsCompany = BeanUtils.toBean(createReqVO, LogisticsCompanyDO.class);
        logisticsCompanyMapper.insert(logisticsCompany);
        // 返回
        return logisticsCompany.getId();
    }

    @Override
    public void updateLogisticsCompany(LogisticsCompanySaveReqVO updateReqVO) {
        // 校验存在
        validateLogisticsCompanyExists(updateReqVO.getId());
        // 更新
        LogisticsCompanyDO updateObj = BeanUtils.toBean(updateReqVO, LogisticsCompanyDO.class);
        logisticsCompanyMapper.updateById(updateObj);
    }

    @Override
    public void deleteLogisticsCompany(Long id) {
        // 校验存在
        validateLogisticsCompanyExists(id);
        // 删除
        logisticsCompanyMapper.deleteById(id);
    }

    private void validateLogisticsCompanyExists(Long id) {
        if (logisticsCompanyMapper.selectById(id) == null) {
            throw exception(LOGISTICS_COMPANY_NOT_EXISTS);
        }
    }

    @Override
    public LogisticsCompanyDO getLogisticsCompany(Long id) {
        return logisticsCompanyMapper.selectById(id);
    }

    @Override
    public PageResult<LogisticsCompanyDO> getLogisticsCompanyPage(LogisticsCompanyPageReqVO pageReqVO) {
        return logisticsCompanyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<LogisticsCompanyDO> getLogisticsCompanyListByStatus(Integer status) {
        return logisticsCompanyMapper.selectListByStatus(status);
    }

    @Override
    public List<LogisticsCompanyDO> getLogisticsCompanyList(Collection<Long> companyIds) {
        return logisticsCompanyMapper.selectBatchIds(companyIds);
    }
}