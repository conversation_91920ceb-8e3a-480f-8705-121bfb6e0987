package cn.iocoder.yudao.module.agent.dal.mysql.transferitem;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.agent.dal.dataobject.transferitem.AgentTransferItemDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 代购转运单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AgentTransferItemMapper extends BaseMapperX<AgentTransferItemDO> {

    default List<AgentTransferItemDO> selectListByTransferId(Long transferId) {
        return selectList(AgentTransferItemDO::getTransferId, transferId);
    }

    default int deleteByTransferId(Long transferId) {
        return delete(AgentTransferItemDO::getTransferId, transferId);
    }

}