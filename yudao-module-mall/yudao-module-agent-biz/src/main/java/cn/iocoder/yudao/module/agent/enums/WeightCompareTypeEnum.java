package cn.iocoder.yudao.module.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 重量比较类型枚举
 * 用于定义体积重和实际重量的比较规则
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WeightCompareTypeEnum {

    /**
     * 取较大者
     * 包裹实际重量和体积重量相比，取较大者计算
     */
    MAX("MAX", "取较大者", "包裹实际重量和体积重量相比，取较大者计算"),

    /**
     * 双倍阈值规则
     * 体积重低于实际重量2倍的，按照实际重量收费；
     * 达到或超过实际重量2倍的，按照体积重量收取
     */
    DOUBLE_THRESHOLD("DOUBLE_THRESHOLD", "双倍阈值规则",
                    "体积重低于实际重量2倍的，按照实际重量收费；达到或超过实际重量2倍的，按照体积重量收取"),

    /**
     * 泡重比规则
     * 针对≤2kg货物，若泡重比≤1.5，免泡，按实际重量计费；若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费
     * 针对>2kg货物，按包裹实际重量和体积重量相比，取较大者计费
     * (泡比计算方式与行业通用标准一致：（长*宽*高）/8000/实重)
     */
    BUBBLE_RATIO("BUBBLE_RATIO", "泡重比规则",
                "针对≤2kg货物，若泡重比≤1.5，免泡，按实际重量计费；若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费；针对>2kg货物，按包裹实际重量和体积重量相比，取较大者计费");

    /**
     * 类型编码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在返回null
     */
    public static WeightCompareTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WeightCompareTypeEnum value : values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 验证编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 计算计费重量
     *
     * @param actualWeight 实际重量(g)
     * @param volumeWeight 体积重(g)
     * @return 计费重量(g)
     */
    public Integer calculateChargeableWeight(Integer actualWeight, Integer volumeWeight) {
        if (actualWeight == null || actualWeight <= 0) {
            return volumeWeight != null ? volumeWeight : 0;
        }

        if (volumeWeight == null || volumeWeight <= 0) {
            return actualWeight;
        }

        switch (this) {
            case MAX:
                return Math.max(actualWeight, volumeWeight);

            case DOUBLE_THRESHOLD:
                // 体积重低于实际重量2倍的，按照实际重量收费；达到或超过实际重量2倍的，按照体积重量收取
                return volumeWeight < actualWeight * 2 ? actualWeight : volumeWeight;

            case BUBBLE_RATIO:
                return calculateBubbleRatioWeight(actualWeight, volumeWeight);

            default:
                return Math.max(actualWeight, volumeWeight);
        }
    }

    /**
     * 计算泡重比规则的计费重量
     * 针对≤2kg货物，若泡重比≤1.5，免泡，按实际重量计费；若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费
     * 针对>2kg货物，按包裹实际重量和体积重量相比，取较大者计费
     * (泡比计算方式与行业通用标准一致：（长*宽*高）/8000/实重)
     *
     * @param actualWeight 实际重量(g)
     * @param volumeWeight 体积重(g)
     * @return 计费重量(g)
     */
    private Integer calculateBubbleRatioWeight(Integer actualWeight, Integer volumeWeight) {
        // 针对>2kg货物，直接按包裹实际重量和体积重量相比，取较大者计费
        if (actualWeight > 2000) {
            return Math.max(actualWeight, volumeWeight);
        }

        // 针对≤2kg货物，需要计算泡重比
        // 泡重比 = 体积重 / 实际重量
        double bubbleRatio = (double) volumeWeight / actualWeight;

        if (bubbleRatio <= 1.5) {
            // 若泡重比≤1.5，免泡，按实际重量计费
            return actualWeight;
        } else {
            // 若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费
            return Math.max(actualWeight, volumeWeight);
        }
    }
}
