package cn.iocoder.yudao.module.agent.service.shipping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsCompany.LogisticsCompanyDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsZone.LogisticsZoneDO;
import cn.iocoder.yudao.module.agent.service.logisticsCompany.LogisticsCompanyService;
import cn.iocoder.yudao.module.agent.service.logisticsZone.LogisticsZoneService;
import cn.iocoder.yudao.module.agent.service.shipping.bo.LogisticsPlanBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一运费计算服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShippingCalculationServiceImpl implements ShippingCalculationService {

    @Resource
    private ShippingQuoteService shippingQuoteService;
    
    @Resource
    private LogisticsZoneService logisticsZoneService;
    
    @Resource
    private LogisticsCompanyService logisticsCompanyService;

    @Override
    public List<ShippingQuoteRespBO> calculateShippingQuotes(ShippingCalculationReqBO reqBO, boolean includeUnavailable) {
        log.info("=== 开始统一运费计算 ===");
        log.info("计算参数: 国家={}, 重量={}g, 邮编={}, 包含不可用={}", 
                reqBO.getCountryCode(), reqBO.getWeight(), reqBO.getPostalCode(), includeUnavailable);

        try {
            // 1. 获取基础运费报价（不考虑分区）
            List<ShippingQuoteRespBO> baseQuotes = shippingQuoteService.getShippingQuotes(reqBO);
            log.info("获取到{}个基础报价", baseQuotes.size());

            if (CollUtil.isEmpty(baseQuotes)) {
                log.warn("国家{}暂无基础运费方案", reqBO.getCountryCode());
                return new ArrayList<>();
            }

            // 2. 处理分区逻辑（如果有邮编）
            List<ShippingQuoteRespBO> processedQuotes = processZoneLogic(baseQuotes, reqBO);

            // 3. 过滤结果
            if (!includeUnavailable) {
                processedQuotes = processedQuotes.stream()
                        .filter(quote -> Boolean.TRUE.equals(quote.getAvailable()))
                        .collect(Collectors.toList());
            }

            log.info("=== 统一运费计算完成 ===");
            log.info("最终返回{}个报价，其中可用{}个", processedQuotes.size(),
                    processedQuotes.stream().mapToInt(q -> Boolean.TRUE.equals(q.getAvailable()) ? 1 : 0).sum());

            return processedQuotes;

        } catch (Exception e) {
            log.error("统一运费计算失败: 国家={}, 重量={}g", reqBO.getCountryCode(), reqBO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AppShippingQuoteRespVO> getAppShippingQuotes(ShippingCalculationReqBO reqBO) {
        // 获取所有报价（包含不可用）
        List<ShippingQuoteRespBO> quotes = calculateShippingQuotes(reqBO, true);
        
        if (CollUtil.isEmpty(quotes)) {
            return new ArrayList<>();
        }

        // 获取物流公司信息
        Map<Long, LogisticsCompanyDO> companyMap = getLogisticsCompanyMap(quotes);
        
        // 转换为用户端响应对象
        return convertBOToAppResponse(quotes, companyMap);
    }

    @Override
    public List<LogisticsPlanBO> getAvailableLogisticsPlans(ShippingCalculationReqBO reqBO) {
        // 获取仅可用报价
        List<ShippingQuoteRespBO> availableQuotes = calculateShippingQuotes(reqBO, false);
        
        if (CollUtil.isEmpty(availableQuotes)) {
            return new ArrayList<>();
        }

        // 转换为LogisticsPlanBO列表
        return availableQuotes.stream()
                .map(this::convertToLogisticsPlanFromBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Integer calculateAdditionalFee(String zoneCode, String restrictionType, String feeFormula, Integer weight, Integer basePrice) {
        if (!"REMOTE_FEE".equals(restrictionType) || StrUtil.isBlank(feeFormula)) {
            return 0;
        }

        try {
            // 解析费用公式，例如：3*weight_kg,min:48 表示每公斤3元，最低48元
            return parseAndCalculateFeeFormula(feeFormula, weight, basePrice);
        } catch (Exception e) {
            log.error("计算附加费失败: zoneCode={}, formula={}, weight={}", zoneCode, feeFormula, weight, e);
            return 0;
        }
    }

    @Override
    public String checkZoneRestriction(String zoneCode, String restrictionType) {
        if ("FORBIDDEN".equals(restrictionType)) {
            return "该地区禁止配送";
        }
        return null; // 可配送
    }

    /**
     * 处理分区逻辑
     */
    private List<ShippingQuoteRespBO> processZoneLogic(List<ShippingQuoteRespBO> baseQuotes, ShippingCalculationReqBO reqBO) {
        // 如果没有邮编，直接返回基础报价
        if (StrUtil.isBlank(reqBO.getPostalCode())) {
            log.info("无邮编信息，跳过分区处理");
            return baseQuotes;
        }

        List<ShippingQuoteRespBO> processedQuotes = new ArrayList<>();

        for (ShippingQuoteRespBO quote : baseQuotes) {
            try {
                // 查询该产品在该国家的分区信息
                LogisticsZoneDO zone = logisticsZoneService.findZoneByPostalCode(
                        reqBO.getCountryCode(), quote.getId(), reqBO.getPostalCode());

                if (zone == null) {
                    // 没有找到分区，使用原始报价
                    processedQuotes.add(quote);
                    continue;
                }

                // 检查分区限制
                String restriction = checkZoneRestriction(zone.getZoneCode(), zone.getRestrictionType());
                if (restriction != null) {
                    // 该分区禁止配送
                    quote.setAvailable(false);
                    quote.setUnavailableReason(restriction);
                    processedQuotes.add(quote);
                    continue;
                }

                // 计算附加费
                Integer additionalFee = calculateAdditionalFee(
                        zone.getZoneCode(), zone.getRestrictionType(), 
                        zone.getFeeFormula(), reqBO.getWeight(), quote.getTotal());

                if (additionalFee > 0) {
                    // 应用附加费
                    quote.setAdditionalFee(additionalFee);
                    quote.setTotal(quote.getTotal() + additionalFee);
                    log.info("应用分区附加费: 产品={}, 分区={}, 附加费={}分", 
                            quote.getName(), zone.getZoneCode(), additionalFee);
                }

                processedQuotes.add(quote);

            } catch (Exception e) {
                log.error("处理产品分区逻辑失败: productId={}, quoteName={}", quote.getId(), quote.getName(), e);
                // 出错时保留原始报价
                processedQuotes.add(quote);
            }
        }

        return processedQuotes;
    }

    /**
     * 解析并计算费用公式
     */
    private Integer parseAndCalculateFeeFormula(String feeFormula, Integer weight, Integer basePrice) {
        // 简化实现，支持常见格式：
        // "3*weight_kg,min:48" - 每公斤3元，最低48元
        // "fixed:100" - 固定100分
        // "percent:10" - 基础价格的10%

        if (feeFormula.startsWith("fixed:")) {
            return Integer.parseInt(feeFormula.substring(6));
        }

        if (feeFormula.startsWith("percent:")) {
            int percent = Integer.parseInt(feeFormula.substring(8));
            return basePrice * percent / 100;
        }

        if (feeFormula.contains("weight_kg")) {
            // 解析类似 "3*weight_kg,min:48" 的格式
            String[] parts = feeFormula.split(",");
            String weightPart = parts[0].trim();
            
            if (weightPart.contains("*weight_kg")) {
                String rateStr = weightPart.replace("*weight_kg", "").trim();
                int rate = Integer.parseInt(rateStr);
                int weightKg = (int) Math.ceil(weight / 1000.0); // 转换为公斤并向上取整
                int calculatedFee = rate * weightKg;

                // 检查最小值限制
                if (parts.length > 1 && parts[1].trim().startsWith("min:")) {
                    int minFee = Integer.parseInt(parts[1].trim().substring(4));
                    calculatedFee = Math.max(calculatedFee, minFee);
                }

                return calculatedFee;
            }
        }

        log.warn("不支持的费用公式格式: {}", feeFormula);
        return 0;
    }

    /**
     * 获取物流公司信息映射
     */
    private Map<Long, LogisticsCompanyDO> getLogisticsCompanyMap(List<ShippingQuoteRespBO> quotes) {
        Set<Long> companyIds = quotes.stream()
                .map(ShippingQuoteRespBO::getCompanyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
                
        if (CollUtil.isEmpty(companyIds)) {
            return new HashMap<>();
        }
        
        List<LogisticsCompanyDO> companies = logisticsCompanyService.getLogisticsCompanyList(companyIds);
        return companies.stream()
                .collect(Collectors.toMap(LogisticsCompanyDO::getId, company -> company));
    }

    /**
     * 将BO转换为用户端响应VO
     */
    private List<AppShippingQuoteRespVO> convertBOToAppResponse(List<ShippingQuoteRespBO> boQuotes, Map<Long, LogisticsCompanyDO> companyMap) {
        return boQuotes.stream()
                .map(boQuote -> {
                    // 使用BeanUtils进行对象转换，利用继承关系避免字段复制
                    AppShippingQuoteRespVO appQuote = BeanUtils.toBean(boQuote, AppShippingQuoteRespVO.class);
                    
                    // 处理图标URL：如果产品没有图标，则使用物流公司的图标
                    if (appQuote.getIconUrl() == null) {
                        LogisticsCompanyDO company = companyMap.get(boQuote.getCompanyId());
                        if (company != null) {
                            appQuote.setIconUrl(company.getIconUrl());
                        }
                    }
                    
                    return appQuote;
                })
                .collect(Collectors.toList());
    }

    /**
     * 将ShippingQuoteRespBO转换为LogisticsPlanBO
     */
    private LogisticsPlanBO convertToLogisticsPlanFromBO(ShippingQuoteRespBO quote) {
        if (quote == null) {
            return null;
        }

        try {
            LogisticsPlanBO plan = new LogisticsPlanBO();
            
            // 基础信息
            plan.setId(quote.getId());
            plan.setPriceId(quote.getPriceId());
            plan.setName(quote.getName());
            plan.setIconUrl(quote.getIconUrl());
            plan.setTransitTime(quote.getTransitTime());
            plan.setAvailable(quote.getAvailable());
            plan.setUnavailableReason(quote.getUnavailableReason());

            // 费用信息
            plan.setChargeableWeight(quote.getChargeableWeight());
            plan.setTotalFee(quote.getTotal());
            plan.setFreight(quote.getFreight());
            plan.setOperationFee(quote.getOperationFee());
            plan.setServiceFee(quote.getServiceFee());
            plan.setCustomsFee(quote.getCustomsFee());
            plan.setFuelFee(quote.getFuelFee());
            plan.setAdditionalFee(quote.getAdditionalFee());
            plan.setDeliveryRate(quote.getDeliveryRate());

            // 基础运费设为总费用
            plan.setBasePrice(plan.getTotalFee());

            // 限制信息
            plan.setMinWeight(quote.getMinWeight());
            plan.setMaxWeight(quote.getMaxWeight());

            return plan;

        } catch (Exception e) {
            log.error("转换物流方案失败: productId={}, quoteName={}", quote.getId(), quote.getName(), e);
            return null;
        }
    }
}
