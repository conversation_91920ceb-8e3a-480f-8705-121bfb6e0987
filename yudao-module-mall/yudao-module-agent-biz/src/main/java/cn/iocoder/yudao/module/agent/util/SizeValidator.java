package cn.iocoder.yudao.module.agent.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 尺寸校验器
 * 
 * 用于校验包裹尺寸是否符合物流产品的限制规则
 * 
 * <AUTHOR>
 */
@Slf4j
public class SizeValidator {

    /**
     * 校验尺寸是否符合限制规则
     * 
     * @param rule 尺寸限制规则
     * @param length 长度（cm）
     * @param width 宽度（cm）
     * @param height 高度（cm）
     * @return 校验结果
     */
    public static ValidationResult validate(SizeLimitRule rule, 
                                          BigDecimal length, 
                                          BigDecimal width, 
                                          BigDecimal height) {
        if (rule == null) {
            return new ValidationResult(true, new ArrayList<>(), null);
        }
        
        List<String> errors = new ArrayList<>();
        Integer oversizeFee = null;

        // 转为整数（假设输入为 cm）
        int l = length != null ? length.intValue() : 0;
        int w = width != null ? width.intValue() : 0;
        int h = height != null ? height.intValue() : 0;

        log.debug("开始尺寸校验: 长={}cm, 宽={}cm, 高={}cm", l, w, h);

        // 1. 最小尺寸限制校验
        if (rule.getMinLength() != null && l < rule.getMinLength()) {
            errors.add("长度不能小于：" + rule.getMinLength() + "cm");
        }
        if (rule.getMinWidth() != null && w < rule.getMinWidth()) {
            errors.add("宽度不能小于：" + rule.getMinWidth() + "cm");
        }
        if (rule.getMinHeight() != null && h < rule.getMinHeight()) {
            errors.add("高度不能小于：" + rule.getMinHeight() + "cm");
        }

        // 2. 基础三维限制校验
        boolean hasBasicOversize = false;
        if (rule.getMaxLength() != null && l > rule.getMaxLength()) {
            errors.add("长度超过限制：" + rule.getMaxLength() + "cm");
            hasBasicOversize = true;
        }
        if (rule.getMaxWidth() != null && w > rule.getMaxWidth()) {
            errors.add("宽度超过限制：" + rule.getMaxWidth() + "cm");
            hasBasicOversize = true;
        }
        if (rule.getMaxHeight() != null && h > rule.getMaxHeight()) {
            errors.add("高度超过限制：" + rule.getMaxHeight() + "cm");
            hasBasicOversize = true;
        }

        // 3. 单边最大限制校验
        if (rule.hasMaxSingleSide()) {
            int maxSide = Math.max(l, Math.max(w, h));
            if (maxSide > rule.getMaxSingleSide()) {
                errors.add("单边长度不能超过：" + rule.getMaxSingleSide() + "cm");
                hasBasicOversize = true;
            }
        }

        // 4. 三边和限制校验
        if (rule.hasMaxDimensionSum()) {
            int totalDimension = l + w + h;
            if (totalDimension > rule.getMaxTotalDimension()) {
                errors.add("长+宽+高不能超过：" + rule.getMaxTotalDimension() + "cm");
                hasBasicOversize = true;
            }
        }

        // 5. 长 + 2*(宽+高) 限制校验
        if (rule.hasLengthPlusDoubleGirth()) {
            int girth = l + 2 * (w + h);
            if (girth > rule.getMaxLengthPlusDoubleGirth()) {
                errors.add("长+2*(宽+高)不能超过：" + rule.getMaxLengthPlusDoubleGirth() + "cm");
                hasBasicOversize = true;
            }
        }

        // 6. 第二长边限制校验（如加拿大）
        if (rule.hasMaxSecondLongestSide()) {
            List<Integer> sides = Arrays.asList(l, w, h);
            sides.sort(Collections.reverseOrder());
            if (sides.size() >= 2 && sides.get(1) > rule.getMaxSecondLongestSide()) {
                errors.add("第二长边不能超过：" + rule.getMaxSecondLongestSide() + "cm");
                hasBasicOversize = true;
            }
        }

        // 7. 处理超尺寸费用
        if (hasBasicOversize && rule.hasOversizeFee()) {
            oversizeFee = rule.getOversizeFee();
            log.info("检测到超尺寸，需要收取附加费: {}分", oversizeFee);
            
            // 如果需要询价，添加特殊提示
            if (Boolean.TRUE.equals(rule.getOversizeInquiryRequired())) {
                errors.add("超尺寸包裹需要单独询价，请联系客服");
            }
        }

        boolean isValid = errors.isEmpty() || (hasBasicOversize && rule.hasOversizeFee() && !Boolean.TRUE.equals(rule.getOversizeInquiryRequired()));
        
        log.debug("尺寸校验完成: 是否通过={}, 错误数量={}, 超尺寸费用={}分", isValid, errors.size(), oversizeFee);
        
        return new ValidationResult(isValid, errors, oversizeFee);
    }

    /**
     * 校验结果
     */
    @Data
    public static class ValidationResult {
        /**
         * 是否通过校验
         */
        private boolean valid;
        
        /**
         * 错误信息列表
         */
        private List<String> messages;
        
        /**
         * 超尺寸附加费（分）
         */
        private Integer oversizeFee;

        public ValidationResult(boolean valid, List<String> messages, Integer oversizeFee) {
            this.valid = valid;
            this.messages = messages != null ? messages : new ArrayList<>();
            this.oversizeFee = oversizeFee;
        }
        
        /**
         * 是否有超尺寸费用
         */
        public boolean hasOversizeFee() {
            return oversizeFee != null && oversizeFee > 0;
        }
        
        /**
         * 获取错误信息字符串
         */
        public String getErrorMessage() {
            return messages.isEmpty() ? null : String.join("; ", messages);
        }
    }
}
