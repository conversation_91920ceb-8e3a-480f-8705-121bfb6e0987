package cn.iocoder.yudao.module.agent.service.shipping;

import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.LogisticsPlanBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;

import java.util.List;

/**
 * 统一运费计算服务接口
 * 
 * 提供统一的运费计算方法，支持不同场景的调用：
 * 1. 运费查询页面：返回所有报价（包含不可用记录）
 * 2. 订单结算：返回仅可用报价，支持精确的邮编分区计算
 * 
 * <AUTHOR>
 */
public interface ShippingCalculationService {

    /**
     * 计算运费报价（通用方法）
     * 
     * 核心计算流程：
     * 1. 根据国家和邮编查询分区信息
     * 2. 处理分区限制（禁止配送、偏远地区等）
     * 3. 查询适用的价格规则
     * 4. 计算基础运费
     * 5. 计算偏远地区附加费
     * 6. 应用折扣
     * 7. 构建最终报价
     * 
     * @param reqBO 运费计算请求参数
     * @param includeUnavailable 是否包含不可用的报价
     * @return 运费报价列表
     */
    List<ShippingQuoteRespBO> calculateShippingQuotes(ShippingCalculationReqBO reqBO, boolean includeUnavailable);

    /**
     * 获取用户端运费查询结果（包含不可用记录）
     * 
     * @param reqBO 运费计算请求参数
     * @return 用户端运费查询响应列表
     */
    List<AppShippingQuoteRespVO> getAppShippingQuotes(ShippingCalculationReqBO reqBO);

    /**
     * 获取可用的物流方案（仅可用记录，用于订单结算）
     * 
     * @param reqBO 运费计算请求参数
     * @return 可用的物流方案列表
     */
    List<LogisticsPlanBO> getAvailableLogisticsPlans(ShippingCalculationReqBO reqBO);

    /**
     * 根据分区信息计算附加费
     * 
     * @param zoneCode 分区编码
     * @param restrictionType 限制类型
     * @param feeFormula 费用公式
     * @param weight 重量（克）
     * @param basePrice 基础价格（分）
     * @return 附加费（分）
     */
    Integer calculateAdditionalFee(String zoneCode, String restrictionType, String feeFormula, Integer weight, Integer basePrice);

    /**
     * 检查分区配送限制
     * 
     * @param zoneCode 分区编码
     * @param restrictionType 限制类型
     * @return 检查结果：null表示可配送，非null表示限制原因
     */
    String checkZoneRestriction(String zoneCode, String restrictionType);
}
