package cn.iocoder.yudao.module.agent.service.shipping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteReqVO;
import cn.iocoder.yudao.module.agent.controller.app.shipping.vo.AppShippingQuoteRespVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsCompany.LogisticsCompanyDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductPriceDO;
import cn.iocoder.yudao.module.agent.dal.mysql.logisticsProduct.LogisticsProductMapper;
import cn.iocoder.yudao.module.agent.dal.mysql.logisticsProduct.LogisticsProductPriceMapper;
import cn.iocoder.yudao.module.agent.enums.logistics.AgentLogisticsPriceTypeEnum;
import cn.iocoder.yudao.module.agent.service.logisticsCompany.LogisticsCompanyService;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import cn.iocoder.yudao.module.agent.service.logisticsZone.LogisticsZoneService;
import cn.iocoder.yudao.module.agent.service.shipping.bo.LogisticsPlanBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingQuoteRespBO;
import cn.iocoder.yudao.module.agent.enums.WeightCompareTypeEnum;
import cn.iocoder.yudao.module.agent.util.CategoryRestrictionUtil;
import cn.iocoder.yudao.module.agent.util.SizeRestrictionUtil;
import cn.iocoder.yudao.module.agent.util.TieredIncrementalPriceCalculator;
import cn.iocoder.yudao.module.agent.util.TieredPriceCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLang;

/**
 * 运费查询服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShippingQuoteServiceImpl implements ShippingQuoteService {

    @Resource
    private LogisticsProductMapper logisticsProductMapper;

    @Resource
    private LogisticsProductPriceMapper logisticsProductPriceMapper;

    @Resource
    private LogisticsProductService logisticsProductService;

    @Resource
    private LogisticsCompanyService logisticsCompanyService;

    @Resource
    private LogisticsZoneService logisticsZoneService;


    /**
     * 查询运费报价（用户端 运费查询页面）
     */
    @Override
    public List<AppShippingQuoteRespVO> getAppShippingQuotes(AppShippingQuoteReqVO reqVO) {
        log.info("=== 开始用户端运费查询 ===");
        log.info("用户端查询参数: 国家={}, 重量={}g, 长宽高=[{},{},{}], 分类={}",
                reqVO.getCountryCode(), reqVO.getWeight(), reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight(), reqVO.getCategoryIds());

        try {
            // 1. 转换为内部请求对象并获取运费报价
            ShippingCalculationReqBO internalReqVO = BeanUtils.toBean(reqVO, ShippingCalculationReqBO.class);
            List<ShippingQuoteRespBO> internalQuotes = getShippingQuotes(internalReqVO);
            log.info("内部查询完成，获得{}个报价", internalQuotes.size());

            if (CollUtil.isEmpty(internalQuotes)) {
                return new ArrayList<>();
            }

            // 2. 批量获取物流公司信息
            Map<Long, LogisticsCompanyDO> companyMap = getLogisticsCompanyMap(internalQuotes);

            // 3. 转换为用户端响应对象
            return convertBOToAppResponse(internalQuotes, companyMap);

        } catch (Exception e) {
            log.error("用户端运费查询失败: 国家={}, 重量={}g", reqVO.getCountryCode(), reqVO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 用户端包裹计算运费 仅获取可用物流方案 （创建包裹使用）
     */
    @Override
    public List<LogisticsPlanBO> getEnableShippingPlans(ShippingCalculationReqBO reqVO) {
        log.info("=== 开始获取可用的物流方案 ===");
        log.info("查询参数: 国家={}, 重量={}g, 邮编={}", reqVO.getCountryCode(), reqVO.getWeight(), reqVO.getPostalCode());

        try {
            // 1. 获取运费报价列表并过滤可用的报价
            List<ShippingQuoteRespBO> availableQuotes = getShippingQuotes(reqVO).stream()
                    .filter(quote -> Boolean.TRUE.equals(quote.getAvailable()))
                    .collect(Collectors.toList());
            log.info("获取到{}个可用运费报价", availableQuotes.size());

            if (CollUtil.isEmpty(availableQuotes)) {
                log.warn("国家{}暂无可用的物流方案", reqVO.getCountryCode());
                return new ArrayList<>();
            }

            // 2. 转换为LogisticsPlanBO列表
            List<LogisticsPlanBO> plans = availableQuotes.stream()
                    .map(this::convertToLogisticsPlanFromBO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("=== 物流方案获取完成 ===");
            log.info("总共返回{}个方案，其中可用{}个", plans.size(),
                    plans.stream().mapToInt(p -> Boolean.TRUE.equals(p.getAvailable()) ? 1 : 0).sum());

            return plans;

        } catch (Exception e) {
            log.error("获取物流方案失败: 国家={}, 重量={}g", reqVO.getCountryCode(), reqVO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ShippingQuoteRespBO> getShippingQuotes(ShippingCalculationReqBO reqVO) {
        log.info("=== 开始查询运费报价 ===");
        log.info("查询参数: 国家={}, 重量={}g, 长宽高=[{},{},{}], 分类={}",
                reqVO.getCountryCode(), reqVO.getWeight(),
                reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight(),
                reqVO.getCategoryIds());

        try {
            // 1. 直接查询该国家的所有有效价格规则，避免冗余查询
            List<LogisticsProductPriceDO> availablePrices = getAvailablePricesByCountry(reqVO.getCountryCode(), reqVO.getPostalCode());
            log.info("查询到{}条价格规则", availablePrices.size());

            if (CollUtil.isEmpty(availablePrices)) {
                log.warn("国家{}暂无可用的物流方案", reqVO.getCountryCode());
                return new ArrayList<>();
            }

            // 打印价格规则详情并验证数据完整性
            for (LogisticsProductPriceDO price : availablePrices) {
                log.info("价格规则详情: productId={}, priceId={}, 重量限制=[{}-{}]g, 状态={}, 价格类型={}", price.getProductId(), price.getId(), price.getMinWeight(), price.getMaxWeight(), price.getStatus(), price.getPriceType());
                // 验证价格规则数据完整性
                validatePriceRuleData(price);
            }

            // 2. 批量获取相关的物流产品信息，减少数据库查询次数
            Set<Long> productIds = availablePrices.stream()
                    .map(LogisticsProductPriceDO::getProductId)
                    .collect(Collectors.toSet());
            log.info("需要查询的产品ID: {}", productIds);

            Map<Long, LogisticsProductDO> productMap = getProductMapByIds(productIds);
            log.info("查询到{}个产品信息", productMap.size());

            // 打印产品详情
            productMap.forEach((id, product) -> {
                log.info("产品详情: id={}, name={}, status={}, sort={}", id, product.getNameZh(), product.getStatus(), product.getSort());
            });

            // 3. 并行计算运费报价，提高计算效率
            List<ShippingQuoteRespBO> quotes = availablePrices.parallelStream()
                    .map(priceRule -> calculateQuoteForPriceRuleBO(priceRule, productMap.get(priceRule.getProductId()), reqVO))
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(ShippingQuoteRespBO::getSort, Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());

            // 4. 去除同一线路（productId）中的重复项，只保留 feeDetail.total 最便宜的一条
            List<ShippingQuoteRespBO> uniqueQuotes = quotes.stream()
                    .collect(Collectors.toMap(
                            ShippingQuoteRespBO::getId,  // 以 productId 作为 key
                            quote -> quote,                     // value 为对象本身
                            (quote1, quote2) ->{
                                // 比较两个 quote 的 total，选择更便宜的
                                Integer total1 = quote1.getTotal();
                                Integer total2 = quote2.getTotal();

                                // 处理 null 情况：null 视为最大值（即更贵）
                                if (total1 == null) return quote2;
                                if (total2 == null) return quote1;
                                return total1 <= total2 ? quote1 : quote2;
                            }
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            // 5. 最终排序：推荐优先，然后按价格升序
            List<ShippingQuoteRespBO> result = uniqueQuotes.stream()
                    .sorted(Comparator
                            // 第一级：available = false 的放最后
                            .comparing((ShippingQuoteRespBO quote) -> !Boolean.TRUE.equals(quote.getAvailable()))
                            // 第二级：recommended = true 的优先
                            .thenComparing(quote -> Boolean.FALSE.equals(quote.getRecommended()))
                            // 第三级：total 升序，null 视为最大值
                            .thenComparing(quote -> {
                                Integer total = quote.getTotal();
                                return total != null ? total : Integer.MAX_VALUE;
                            })
                    )
                    .collect(Collectors.toList());

            log.info("=== 运费查询完成 ===总共找到{}个方案，其中可用{}个，不可用{}个",
                    result.size(),
                    result.stream().mapToInt(q -> q.getAvailable() ? 1 : 0).sum(),
                    result.stream().mapToInt(q -> q.getAvailable() ? 0 : 1).sum());
            return result;

        } catch (Exception e) {
            log.error("运费查询失败: 国家={}, 重量={}g", reqVO.getCountryCode(), reqVO.getWeight(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据国家编码获取所有可用的价格规则
     *
     * 优化策略：
     * 1. 直接按国家编码查询，避免查询无关数据
     * 2. 只查询启用状态的价格规则
     * 3. 检查生效时间和失效时间，确保价格规则在有效期内
     * 4. 按产品排序和创建时间排序，确保结果稳定
     *
     * @param countryCode 国家编码
     * @param postalCode   邮编
     * @return 该国家的所有有效价格规则列表
     */
    private List<LogisticsProductPriceDO> getAvailablePricesByCountry(String countryCode, String postalCode) {
        log.info("查询国家{}的价格规则", countryCode);

        LocalDateTime now = LocalDateTime.now();
        log.info("当前时间: {}", now);

//        //根据国家和邮编获取分区编号（不需要分区则返回null,国家存在多个分区且未传递邮编则返回默认第一个分区）
//        String zoneCode = logisticsZoneService.getZoneCodeByPostalCode(countryCode,postalCode);

        List<LogisticsProductPriceDO> prices = logisticsProductPriceMapper.selectList(
            new LambdaQueryWrapperX<LogisticsProductPriceDO>()
                .eq(LogisticsProductPriceDO::getCountryCode, countryCode)
                .eq(LogisticsProductPriceDO::getStatus, CommonStatusEnum.ENABLE.getStatus()) // 只查询启用状态
                // 生效时间检查：如果设置了生效时间，则当前时间必须大于等于生效时间
                .and(wrapper -> wrapper
                    .isNull(LogisticsProductPriceDO::getEffectiveTime) // 未设置生效时间
                    .or()
                    .le(LogisticsProductPriceDO::getEffectiveTime, now) // 或者当前时间 >= 生效时间
                )
                // 失效时间检查：如果设置了失效时间，则当前时间必须小于失效时间
                .and(wrapper -> wrapper
                    .isNull(LogisticsProductPriceDO::getExpireTime) // 未设置失效时间
                    .or()
                    .gt(LogisticsProductPriceDO::getExpireTime, now) // 或者当前时间 < 失效时间
                )
                .orderByAsc(LogisticsProductPriceDO::getProductId)
                .orderByDesc(LogisticsProductPriceDO::getCreateTime)
        );

        log.info("查询SQL: SELECT * FROM logistics_product_price WHERE country_code = '{}' AND status = 1 AND (effective_time IS NULL OR effective_time <= '{}') AND (expire_time IS NULL OR expire_time > '{}')",
                countryCode, now, now);
        log.info("查询结果: 找到{}条有效价格规则", prices.size());

        // 记录时间过滤的详细信息
        if (log.isDebugEnabled()) {
            for (LogisticsProductPriceDO price : prices) {
                log.debug("价格规则ID={}, 产品ID={}, 生效时间={}, 失效时间={}",
                        price.getId(), price.getProductId(), price.getEffectiveTime(), price.getExpireTime());
            }
        }

        return prices;
    }

    /**
     * 验证价格规则数据完整性
     *
     * 验证逻辑：
     * - 最大重量：null或0表示无限制，负数才是异常
     * - 最小重量：负数是异常
     * - 重量逻辑：最小重量不能大于最大重量（当最大重量>0时）
     *
     * @param priceRule 价格规则
     */
    private void validatePriceRuleData(LogisticsProductPriceDO priceRule) {
        List<String> issues = new ArrayList<>();

        // 检查重量限制
        if (priceRule.getMaxWeight() != null && priceRule.getMaxWeight() < 0) {
            issues.add("最大重量限制异常: " + priceRule.getMaxWeight() + "g (不能为负数)");
        }

        if (priceRule.getMinWeight() != null && priceRule.getMinWeight() < 0) {
            issues.add("最小重量限制异常: " + priceRule.getMinWeight() + "g (不能为负数)");
        }

        // 检查重量逻辑（只有当最大重量大于0时才检查）
        if (priceRule.getMinWeight() != null && priceRule.getMaxWeight() != null
            && priceRule.getMaxWeight() > 0 && priceRule.getMinWeight() > priceRule.getMaxWeight()) {
            issues.add(String.format("重量限制逻辑错误: 最小重量(%dg) > 最大重量(%dg)",
                    priceRule.getMinWeight(), priceRule.getMaxWeight()));
        }

        // 检查价格配置
        if (StrUtil.isNotBlank(priceRule.getPriceConfig())) {
            // 递增价格配置检查
            try {
                JSONUtil.parseArray(priceRule.getPriceConfig());
            } catch (Exception e) {
                issues.add("价格配置JSON格式错误");
            }
        } else if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(priceRule.getPriceType()) || AgentLogisticsPriceTypeEnum.TIERED_INCREMENTAL.getCode().equals(priceRule.getPriceType())) {
            if (StrUtil.isBlank(priceRule.getPriceConfig())) {
                issues.add("阶梯价格类型但阶梯价格配置为空");
            }
        } else {
            if (priceRule.getFirstPrice() == null || priceRule.getFirstPrice() <= 0) {
                issues.add("首重价格配置异常: " + priceRule.getFirstPrice());
            }
            if (priceRule.getFirstUnit() == null || priceRule.getFirstUnit() <= 0) {
                issues.add("首重重量配置异常: " + priceRule.getFirstUnit());
            }
        }

        // 检查状态
        if (priceRule.getStatus() == null) {
            issues.add("状态字段为空");
        }

        // 记录重量限制配置信息
        String weightLimitInfo = String.format("重量限制配置: 最小=%s, 最大=%s",
                priceRule.getMinWeight() != null && priceRule.getMinWeight() > 0 ? priceRule.getMinWeight() + "g" : "无限制",
                priceRule.getMaxWeight() != null && priceRule.getMaxWeight() > 0 ? priceRule.getMaxWeight() + "g" : "无限制");
        log.debug("价格规则 (priceRuleId={}): {}", priceRule.getId(), weightLimitInfo);

        // 如果有问题，记录警告日志
        if (!issues.isEmpty()) {
            log.warn("价格规则数据异常 (priceRuleId={}): {}", priceRule.getId(), String.join("; ", issues));
            log.warn("完整价格规则数据: {}", priceRule);
        }
    }

    /**
     * 批量获取物流产品信息
     *
     * 优化策略：
     * 1. 使用IN查询批量获取，减少数据库交互次数
     * 2. 转换为Map结构，提高后续查找效率
     * 3. 只查询启用状态的产品
     *
     * @param productIds 产品ID集合
     * @return 产品ID到产品信息的映射
     */
    private Map<Long, LogisticsProductDO> getProductMapByIds(Set<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            log.info("产品ID集合为空，返回空Map");
            return new HashMap<>();
        }

        log.info("批量查询产品信息: productIds={}", productIds);

        List<LogisticsProductDO> products = logisticsProductMapper.selectList(
            new LambdaQueryWrapperX<LogisticsProductDO>()
                .in(LogisticsProductDO::getId, productIds)
                .eq(LogisticsProductDO::getStatus, 0) // 0表示启用状态
        );

        log.info("查询SQL: SELECT * FROM logistics_product WHERE id IN ({}) AND status = 0",
                productIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        log.info("查询结果: 找到{}个产品", products.size());

        if (products.size() != productIds.size()) {
            Set<Long> foundIds = products.stream().map(LogisticsProductDO::getId).collect(Collectors.toSet());
            Set<Long> missingIds = new HashSet<>(productIds);
            missingIds.removeAll(foundIds);
            log.warn("部分产品未找到或未启用: missingIds={}", missingIds);
        }

        return products.stream()
                .collect(Collectors.toMap(LogisticsProductDO::getId, product -> product));
    }

    /**
     * 快速检查重量限制
     *
     * 业务逻辑：
     * - 最小重量限制：如果设置了且大于0，则检查
     * - 最大重量限制：如果为null或0，表示无限制；如果大于0，则检查
     *
     * @param priceRule 价格规则
     * @param weight 重量(g)
     * @return 如果超限返回错误信息，否则返回null
     */
    private String checkWeightLimits(LogisticsProductPriceDO priceRule, Integer weight) {
        log.debug("检查重量限制详情: priceRuleId={}, 重量={}g, 最小重量={}g, 最大重量={}g",
                priceRule.getId(), weight, priceRule.getMinWeight(), priceRule.getMaxWeight());

        // 检查最小重量限制（如果设置了且大于0）
        if (priceRule.getMinWeight() != null && priceRule.getMinWeight() > 0 && weight < priceRule.getMinWeight()) {
            String errorMsg = String.format("重量%dg低于最小限制%dg", weight, priceRule.getMinWeight());
            log.info("重量检查失败: {}", errorMsg);
            return errorMsg;
        }

        // 检查最大重量限制（只有当设置了且大于0时才检查）
        if (priceRule.getMaxWeight() != null && priceRule.getMaxWeight() > 0 && weight > priceRule.getMaxWeight()) {
            String errorMsg = String.format("重量%dg超过最大限制%dg", weight, priceRule.getMaxWeight());
            log.info("重量检查失败: {}", errorMsg);
            return errorMsg;
        }

        // 记录重量限制状态
        if (priceRule.getMaxWeight() == null || priceRule.getMaxWeight() <= 0) {
            log.debug("该产品无最大重量限制");
        }
        if (priceRule.getMinWeight() == null || priceRule.getMinWeight() <= 0) {
            log.debug("该产品无最小重量限制");
        }

        log.debug("重量检查通过");
        return null;
    }

    /**
     * 检查尺寸限制
     *
     * @param priceRule 价格规则
     * @param length 长度(cm)
     * @param width 宽度(cm)
     * @param height 高度(cm)
     * @return 如果超限返回错误信息，否则返回null
     */
    private String checkSizeRestrictions(LogisticsProductPriceDO priceRule, BigDecimal length, BigDecimal width, BigDecimal height) {
        log.debug("检查尺寸限制详情: priceRuleId={}, 长={}cm, 宽={}cm, 高={}cm, 尺寸限制配置={}", priceRule.getId(), length, width, height, priceRule.getSizeRestrictions());

        // 如果没有提供完整的尺寸信息，跳过尺寸检查
        if (length == null || width == null || height == null) {
            log.debug("尺寸信息不完整，跳过尺寸限制检查");
            return null;
        }

        // 如果没有配置尺寸限制，通过检查
        if (StrUtil.isBlank(priceRule.getSizeRestrictions())) {
            log.debug("该产品无尺寸限制配置");
            return null;
        }

        try {
            // 使用新的校验器进行尺寸检查
            SizeRestrictionUtil.SizeCheckResult result = SizeRestrictionUtil.checkSizeWithNewValidator(length, width, height, priceRule.getSizeRestrictions());
            if (!result.isPassed()) {
                String errorMsg = result.getErrorMessage();
                log.info("尺寸限制检查失败: {}", errorMsg);
                return errorMsg;
            }
            log.debug("尺寸限制检查通过");
            return null;

        } catch (Exception e) {
            log.error("尺寸限制检查异常: priceRuleId={}, 配置={}", priceRule.getId(), priceRule.getSizeRestrictions(), e);
            // 异常情况下不阻止运费计算，但记录错误
            return null;
        }
    }

    /**
     * 计算计费重量
     * 根据weightCompareType规则计算最终的计费重量
     *
     * @param actualWeight 实际重量(g)
     * @param volumeWeight 体积重(g)
     * @param weightCompareType 重量比较类型
     * @return 计费重量(g)
     */
    private Integer calculateChargeableWeight(Integer actualWeight, Integer volumeWeight, String weightCompareType) {
        log.debug("开始计算计费重量: 实际重量={}g, 体积重={}g, 比较规则={}", actualWeight, volumeWeight, weightCompareType);

        // 尝试使用枚举进行计算
        WeightCompareTypeEnum compareTypeEnum = WeightCompareTypeEnum.getByCode(weightCompareType);

        Integer chargeableWeight;
        if (compareTypeEnum != null) {
            // 使用枚举的计算方法
            chargeableWeight = compareTypeEnum.calculateChargeableWeight(actualWeight, volumeWeight);
            log.debug("使用{}规则({}): 计费重量={}g", compareTypeEnum.getName(), compareTypeEnum.getCode(), chargeableWeight);
        } else {
            // 默认规则：取较大者
            if (actualWeight == null || actualWeight <= 0) {
                chargeableWeight = volumeWeight != null ? volumeWeight : 0;
                log.warn("实际重量无效，使用体积重作为计费重量: {}g", chargeableWeight);
            } else if (volumeWeight == null || volumeWeight <= 0) {
                chargeableWeight = actualWeight;
                log.debug("体积重无效，使用实际重量作为计费重量: {}g", chargeableWeight);
            } else {
                chargeableWeight = Math.max(actualWeight, volumeWeight);
                log.debug("使用默认规则(取较大者): 计费重量={}g", chargeableWeight);
            }

            if (StrUtil.isNotBlank(weightCompareType)) {
                log.warn("未知的重量比较规则: {}，使用默认规则(取较大者)", weightCompareType);
            }
        }

        log.info("计费重量计算完成: 实际重量={}g, 体积重={}g, 规则={}, 最终计费重量={}g",
                actualWeight, volumeWeight, weightCompareType, chargeableWeight);

        return chargeableWeight;
    }

    /**
     * 高效计算体积重
     * <p>
     * 计算逻辑：
     * 1. 如果没有提供完整尺寸信息，体积重为0
     * 2. 使用动态基数公式：长×宽×高(cm) ÷ volumeBase × 1000 = 体积重(g)
     * 3. volumeBase优先级：priceRule.volumeBase > product.volumeBase > 8000(默认)
     *
     * @param reqVO     查询请求
     * @param priceRule 价格规则
     * @return 体积重计算结果
     */
    private Integer calculateVolumeWeight(ShippingCalculationReqBO reqVO, LogisticsProductPriceDO priceRule) {
        // 如果没有提供完整的尺寸信息，体积重为0
        if (reqVO.getLength() == null || reqVO.getWidth() == null || reqVO.getHeight() == null) {
            log.debug("尺寸信息不完整，体积重设为0");
            return 0;
        }
        // 获取体积重计算基数
        Integer volumeBase = (priceRule.getVolumeBase() != null && priceRule.getVolumeBase() > 0) ? priceRule.getVolumeBase() : 8000;

        // 计算体积：长×宽×高(cm)
        BigDecimal volume = reqVO.getLength().multiply(reqVO.getWidth()).multiply(reqVO.getHeight());

        // 计算体积重：体积(cm³) ÷ volumeBase × 1000 = 体积重(g)  向上取整 这里的3表示四舍五入到小数点后3位
        Integer volumeWeight = volume.divide(new BigDecimal(volumeBase), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(1000)).intValue();
        log.info("体积重计算完成: 体积={}cm³, 体积基数={}, 体积重={}g, 实际重量={}g", volume, volumeBase, volumeWeight, reqVO.getWeight());
        return volumeWeight;
    }


    /**
     * 费用计算结果
     */
    private static class FeeCalculationResult {
        private final BigDecimal baseFee;
        private final BigDecimal registrationFee;
        private final boolean useTieredRegistrationFee;
        private final boolean calculationSuccessful;
        private final String failureReason;

        public FeeCalculationResult(BigDecimal baseFee, BigDecimal registrationFee, boolean useTieredRegistrationFee) {
            this.baseFee = baseFee;
            this.registrationFee = registrationFee;
            this.useTieredRegistrationFee = useTieredRegistrationFee;
            this.calculationSuccessful = true;
            this.failureReason = null;
        }

        public FeeCalculationResult(String failureReason) {
            this.baseFee = BigDecimal.ZERO;
            this.registrationFee = BigDecimal.ZERO;
            this.useTieredRegistrationFee = false;
            this.calculationSuccessful = false;
            this.failureReason = failureReason;
        }

        public BigDecimal getBaseFee() { return baseFee; }
        public BigDecimal getRegistrationFee() { return registrationFee; }
        public boolean isUseTieredRegistrationFee() { return useTieredRegistrationFee; }
        public boolean isCalculationSuccessful() { return calculationSuccessful; }
        public String getFailureReason() { return failureReason; }
    }

    /**
     * 计算基础运费和挂号费
     *
     * @param priceRule 价格规则
     * @param chargeableWeight 计费重量
     * @return 费用计算结果
     */
    private FeeCalculationResult calculateBaseFeeAndRegistration(LogisticsProductPriceDO priceRule, Integer chargeableWeight) {
        log.info("=== 开始计算基础运费 ===");
        log.info("价格类型: {}, 计费重量: {}g", priceRule.getPriceType(), chargeableWeight);

        BigDecimal baseFee;
        BigDecimal registrationFee;
        boolean useTieredRegistrationFee = false;

        // 根据价格类型和配置选择计算方式
        String priceConfigJson = getPriceConfigJson(priceRule);

        if (AgentLogisticsPriceTypeEnum.TIERED_INCREMENTAL.getCode().equals(priceRule.getPriceType()) &&
            StrUtil.isNotBlank(priceConfigJson)) {
            log.info("使用阶梯递增价格计算");
            log.info("阶梯递增价格配置: {}", priceConfigJson);
            log.info("默认挂号费配置: {}", priceRule.getRegistrationFee());

            // 阶梯递增价格计算
            TieredIncrementalPriceCalculator.TieredIncrementalResult tieredIncrementalResult =
                TieredIncrementalPriceCalculator.calculateTieredIncrementalPrice(
                    priceConfigJson,
                    chargeableWeight,
                    priceRule.getRegistrationFee()
                );

            // 检查是否找到匹配的阶梯
            if (tieredIncrementalResult.getMatchedTier() == null) {
                String failureReason = String.format("重量%dg不在任何阶梯递增配置范围内", chargeableWeight);
                log.warn("阶梯递增价格计算失败: {}", failureReason);
                return new FeeCalculationResult(failureReason);
            }

            baseFee = tieredIncrementalResult.getBaseFee();
            registrationFee = tieredIncrementalResult.getRegistrationFee();
            useTieredRegistrationFee = tieredIncrementalResult.isUseTieredRegistrationFee();

            log.info("阶梯递增价格计算结果: 基础运费={}分, 挂号费={}分, 使用阶梯挂号费={}",
                    baseFee, registrationFee, useTieredRegistrationFee);

        } else if (AgentLogisticsPriceTypeEnum.TIERED.getCode().equals(priceRule.getPriceType()) &&
                   StrUtil.isNotBlank(priceConfigJson)) {
            log.info("使用纯阶梯价格计算");
            log.info("阶梯价格配置: {}", priceConfigJson);
            log.info("挂号费配置: {}", priceRule.getRegistrationFee());

            // 纯阶梯价格计算
            TieredPriceCalculator.TieredPriceResult tieredResult =
                TieredPriceCalculator.calculateTieredPrice(
                    priceConfigJson,
                    chargeableWeight,
                    priceRule.getRegistrationFee()
                );

            // 检查是否找到匹配的阶梯
            if (tieredResult.getMatchedTier() == null) {
                String failureReason = String.format("重量%dg不在任何阶梯价格配置范围内", chargeableWeight);
                log.warn("纯阶梯价格计算失败: {}", failureReason);
                return new FeeCalculationResult(failureReason);
            }

            baseFee = tieredResult.getBaseFee();
            registrationFee = tieredResult.getRegistrationFee();
            useTieredRegistrationFee = tieredResult.isUseTieredRegistrationFee();

            log.info("纯阶梯价格计算结果: 基础运费={}分, 挂号费={}分, 使用阶梯挂号费={}",
                    baseFee, registrationFee, useTieredRegistrationFee);

        } else {
            log.info("使用首重续重计算");
            log.info("首重: {}g = {}分, 续重: {}g = {}分",
                    priceRule.getFirstUnit(), priceRule.getFirstPrice(),
                    priceRule.getAdditionalUnit(), priceRule.getAdditionalPrice());

            // 首重续重计算
            baseFee = calculateIncrementalFee(priceRule, chargeableWeight);
            registrationFee = priceRule.getRegistrationFee() != null ?
                new BigDecimal(priceRule.getRegistrationFee()) : BigDecimal.ZERO;

            log.info("首重续重计算结果: 基础运费={}分, 挂号费={}分", baseFee, registrationFee);
        }

        log.info("=== 基础运费计算完成 ===");
        return new FeeCalculationResult(baseFee, registrationFee, useTieredRegistrationFee);
    }


    /**
     * 计算递增式运费（首重+续重）
     */
    private BigDecimal calculateIncrementalFee(LogisticsProductPriceDO productPrice, Integer weightGrams) {
        log.info("--- 首重续重详细计算 ---");

        if (productPrice.getFirstUnit() == null || productPrice.getFirstPrice() == null) {
            log.warn("首重配置不完整: 首重重量={}, 首重价格={}",
                    productPrice.getFirstUnit(), productPrice.getFirstPrice());
            return BigDecimal.ZERO;
        }

        // 首重费用
        BigDecimal totalFee = new BigDecimal(productPrice.getFirstPrice());
        log.info("首重费用: {}g = {}分", productPrice.getFirstUnit(), totalFee);

        // 计算续重费用
        if (weightGrams > productPrice.getFirstUnit() &&
            productPrice.getAdditionalUnit() != null &&
            productPrice.getAdditionalPrice() != null) {

            int additionalWeight = weightGrams - productPrice.getFirstUnit();
            int additionalUnits = (int) Math.ceil((double) additionalWeight / productPrice.getAdditionalUnit());
            BigDecimal additionalFee = new BigDecimal(productPrice.getAdditionalPrice()).multiply(new BigDecimal(additionalUnits));

            log.info("续重计算: 超出重量={}g, 续重单位={}g, 续重次数={}, 续重单价={}分",
                    additionalWeight, productPrice.getAdditionalUnit(), additionalUnits, productPrice.getAdditionalPrice());
            log.info("续重费用: {} × {} = {}分", productPrice.getAdditionalPrice(), additionalUnits, additionalFee);

            totalFee = totalFee.add(additionalFee);
        } else {
            log.info("重量{}g未超过首重{}g，无续重费用", weightGrams, productPrice.getFirstUnit());
        }

        log.info("首重续重总费用: {}分", totalFee);
        log.info("--- 首重续重计算完成 ---");
        return totalFee;
    }



    @Override
    public AppShippingQuoteRespVO getAppShippingQuoteDetail(Long id) {
        LogisticsProductPriceDO priceDO = logisticsProductPriceMapper.selectById(id);
        if (priceDO == null) {
            return null;
        }

        LogisticsProductDO productDO = logisticsProductMapper.selectById(priceDO.getProductId());
        if (productDO == null) {
            return null;
        }

        return convertProductToAppResponse(productDO, priceDO, getCurrentLang());
    }

    private AppShippingQuoteRespVO convertProductToAppResponse(LogisticsProductDO logisticsProductDO, LogisticsProductPriceDO logisticsProductPriceDO, String currentLang) {

        AppShippingQuoteRespVO appQuote = new AppShippingQuoteRespVO();

        // 复制基础字段
        appQuote.setId(logisticsProductDO.getId());
        appQuote.setName(getCurrentLangName(logisticsProductDO, currentLang));
        appQuote.setIconUrl(logisticsProductDO.getIconUrl());
        appQuote.setFeatures(getCurrentLangFeature(logisticsProductDO, currentLang) );
        appQuote.setTransitTime(logisticsProductPriceDO.getTransitTime());
        appQuote.setTaxInclude(logisticsProductDO.getTaxInclude());

        appQuote.setMinDeclareValue(logisticsProductPriceDO.getMinDeclareValue());
        appQuote.setMaxDeclareValue(logisticsProductPriceDO.getMaxDeclareValue());
        appQuote.setDefaultDeclareType(logisticsProductDO.getDefaultDeclareType());
        appQuote.setDeclarePerKg(logisticsProductDO.getDeclarePerKg());
        appQuote.setDeclareRatio(logisticsProductDO.getDeclareRatio() != null ? logisticsProductDO.getDeclareRatio() : null);
        appQuote.setIossEnabled(logisticsProductDO.getIossEnabled());
        appQuote.setFreeInsure(logisticsProductDO.getFreeInsure());


        // 转换限制信息
        appQuote.setMinWeight(logisticsProductPriceDO.getMinWeight()>0 ? logisticsProductPriceDO.getMinWeight() : logisticsProductDO.getMinWeight());
        appQuote.setMaxWeight(logisticsProductPriceDO.getMaxWeight()>0 ? logisticsProductPriceDO.getMaxWeight() : logisticsProductDO.getMaxWeight());
        appQuote.setDimensionRestriction(getCurrentLangDimensionRestriction(logisticsProductDO, currentLang));
        appQuote.setVolumeWeightRule(getCurrentLangVolumeWeightRule(logisticsProductDO, currentLang));
        appQuote.setCategoryRestrictions(logisticsProductDO.getCategoryRestrictions());

        return appQuote;
    }

    private String getCurrentLangVolumeWeightRule(LogisticsProductDO logisticsProductDO, String currentLang) {
        switch (currentLang) {
            case "zh":
                return logisticsProductDO.getVolumeWeightRuleZh();
            case "fr":
                return logisticsProductDO.getVolumeWeightRuleFr();
            case "de":
                return logisticsProductDO.getVolumeWeightRuleDe();
            case "es":
                return logisticsProductDO.getVolumeWeightRuleEs();
            case "ar":
                return logisticsProductDO.getVolumeWeightRuleAr();
            case "en":
            default:
                return logisticsProductDO.getVolumeWeightRuleEn();
        }
    }

    private String getCurrentLangDimensionRestriction(LogisticsProductDO logisticsProductDO, String currentLang) {
        switch (currentLang) {
            case "zh":
                return logisticsProductDO.getDimensionRestrictionZh();
            case "fr":
                return logisticsProductDO.getDimensionRestrictionFr();
            case "de":
                return logisticsProductDO.getDimensionRestrictionDe();
            case "es":
                return logisticsProductDO.getDimensionRestrictionEs();
            case "ar":
                return logisticsProductDO.getDimensionRestrictionAr();
            case "en":
            default:
                return logisticsProductDO.getDimensionRestrictionEn();
        }

    }

    private String getCurrentLangFeature(LogisticsProductDO logisticsProductDO, String currentLang) {
        switch (currentLang) {
            case "zh":
                return logisticsProductDO.getFeaturesZh();
            case "fr":
                return logisticsProductDO.getFeaturesFr();
            case "de":
                return logisticsProductDO.getFeaturesDe();
            case "es":
                return logisticsProductDO.getFeaturesEs();
            case "ar":
                return logisticsProductDO.getFeaturesAr();
            case "en":
            default:
                return logisticsProductDO.getFeaturesEn();
        }
    }

    private String getCurrentLangName(LogisticsProductDO logisticsProductDO, String currentLang) {
        switch (currentLang) {
            case "zh":
                return logisticsProductDO.getNameZh();
            case "fr":
                return logisticsProductDO.getNameFr();
            case "de":
                return logisticsProductDO.getNameDe();
            case "es":
                return logisticsProductDO.getNameEs();
            case "ar":
                return logisticsProductDO.getNameAr();
            case "en":
            default:
                return logisticsProductDO.getNameEn();
        }
    }

    /**
     * 将ShippingQuoteRespBO转换为LogisticsPlanBO
     *
     * @param quote 运费报价BO
     * @return 物流方案BO
     */
    private LogisticsPlanBO convertToLogisticsPlanFromBO(ShippingQuoteRespBO quote) {
        if (quote == null) {
            return null;
        }

        try {
            LogisticsPlanBO plan = new LogisticsPlanBO();

            // 基础信息
            plan.setProductId(quote.getId());
            plan.setPriceId(quote.getPriceId());
            plan.setName(quote.getName());
            plan.setIconUrl(quote.getIconUrl());
            plan.setFeatures(quote.getFeatures());
            plan.setTransitTime(quote.getTransitTime());
            plan.setTaxInclude(quote.getTaxInclude());
            plan.setAvailable(quote.getAvailable());
            plan.setUnavailableReason(quote.getUnavailableReason());
            plan.setSort(quote.getSort());
            plan.setFreeInsure(quote.getFreeInsure());
            plan.setMinDeclareValue(quote.getMinDeclareValue());
            plan.setMaxDeclareValue(quote.getMaxDeclareValue());

            // 费用信息 - 直接使用Integer，无需转换
            plan.setChargeableWeight(quote.getChargeableWeight());
            plan.setTotalFee(quote.getTotal());
            plan.setFreight(quote.getFreight());
            plan.setOperationFee(quote.getOperationFee());
            plan.setServiceFee(quote.getServiceFee());
            plan.setCustomsFee(quote.getCustomsFee());
            plan.setFuelFee(quote.getFuelFee());
            plan.setAdditionalFee(quote.getAdditionalFee());
            //妥投率
            plan.setDeliveryRate(quote.getDeliveryRate());

            // 基础运费设为总费用
            plan.setBasePrice(plan.getTotalFee());

            // 限制信息
            plan.setMinWeight(quote.getMinWeight());
            plan.setMaxWeight(quote.getMaxWeight());


            return plan;

        } catch (Exception e) {
            log.error("转换物流方案失败: productId={}, quoteName={}", quote.getId(), quote.getName(), e);
            return null;
        }
    }


    /**
     * 基于价格规则计算运费报价（BO版本）
     *
     * @param priceRule 价格规则
     * @param product 物流产品信息
     * @param reqVO 查询请求
     * @return 运费报价BO，如果不可用则返回null
     */
    private ShippingQuoteRespBO calculateQuoteForPriceRuleBO(LogisticsProductPriceDO priceRule,
                                                            LogisticsProductDO product,
                                                            ShippingCalculationReqBO reqVO) {
        log.info("--- 开始计算产品运费（BO版本） ---");
        log.info("产品: id={}, name={}", product != null ? product.getId() : "null",
                product != null ? product.getNameZh() : "null");

        if (product == null) {
            log.warn("物流产品不存在: productId={}", priceRule.getProductId());
            return null;
        }

        try {
            // 1. 检查重量限制
            String weightCheckResult = checkWeightLimits(priceRule, reqVO.getWeight());
            if (weightCheckResult != null) {
                log.warn("重量检查失败: {}", weightCheckResult);
                return createUnavailableQuoteBO(product, priceRule, weightCheckResult);
            }

            // 2. 检查尺寸限制
            String sizeCheckResult = checkSizeRestrictions(priceRule, reqVO.getLength(), reqVO.getWidth(), reqVO.getHeight());
            if (sizeCheckResult != null) {
                log.warn("尺寸限制检查失败: {}", sizeCheckResult);
                return createUnavailableQuoteBO(product, priceRule, sizeCheckResult);
            }

            // 3. 检查分类限制
            CategoryRestrictionUtil.CategoryCheckResult categoryCheckResult =
                CategoryRestrictionUtil.checkCategoryRestrictions(product.getCategoryRestrictions(), reqVO.getCategoryIds());
            if (!categoryCheckResult.isPassed()) {
                log.warn("分类限制检查失败: {}", categoryCheckResult.getErrorMessage());
                return createUnavailableQuoteBO(product, priceRule, categoryCheckResult.getErrorMessage());
            }

            // 4. 计算体积重和计费重量
            Integer chargeableWeight = reqVO.getWeight();
//            VolumeWeightResult volumeResult = new VolumeWeightResult(null, product.getNeedVolumeCal(), priceRule.getVolumeBase());
            Integer volumeWeight = null;
            if(product.getNeedVolumeCal()){
                if(priceRule.getVolumeBase() == null){
                    // 如果price上没有设置体积重计算基数则使用物流产品重的体积重计算基数
                    priceRule.setVolumeBase(product.getVolumeBase());
                }
                volumeWeight = calculateVolumeWeight(reqVO, priceRule);
                chargeableWeight = calculateChargeableWeight(reqVO.getWeight(), volumeWeight, priceRule.getWeightCompareType());
            }


            // 5. 计算运费详情
            ShippingQuoteRespBO quoteFee = calculateFeeDetailBO(product, priceRule, reqVO, volumeWeight, chargeableWeight);

            // 检查运费计算是否成功
            if (quoteFee == null) {
                log.warn("运费计算失败，该物流渠道不可用: productId={}, priceId={}", priceRule.getProductId(), priceRule.getId());
                return createUnavailableQuoteBO(product, priceRule, "当前重量不在该物流渠道的阶梯配置范围内");
            }

            // 6. 应用折扣费率（如果有配置）
            if (priceRule.getDiscountRate() != null && priceRule.getDiscountRate().compareTo(BigDecimal.ZERO) > 0) {
                Integer totalFee = quoteFee.getTotal();
                quoteFee.setTotal(BigDecimal.valueOf(totalFee).multiply(priceRule.getDiscountRate()).intValue());
                log.info("应用折扣费率: {} -> 调整后总费用: {}", priceRule.getDiscountRate(), totalFee);
            }

            // 7. 补充构建完整的报价响应
            buildQuoteResponseBO(product, priceRule, quoteFee);

            log.info("--- 产品运费计算完成（BO版本） ---");
            return quoteFee;

        } catch (Exception e) {
            log.error("计算运费失败: productId={}, priceId={}", priceRule.getProductId(), priceRule.getId(), e);
            return null;
        }
    }

    /**
     * 创建不可用的运费报价BO
     */
    private ShippingQuoteRespBO createUnavailableQuoteBO(LogisticsProductDO product,
                                                        LogisticsProductPriceDO priceRule,
                                                        String reason) {
        ShippingQuoteRespBO quote = new ShippingQuoteRespBO();
        quote.setId(product.getId());
        quote.setPriceId(priceRule.getId());
        quote.setName(product.getNameZh());
        quote.setCompanyId(product.getCompanyId());
        quote.setIconUrl(product.getIconUrl());
        quote.setFeatures(product.getFeaturesZh());
        quote.setAvailable(false);

        quote.setUnavailableReason(reason);
        quote.setSort(product.getSort());
        quote.setTransitTime(priceRule.getTransitTime());
        quote.setServiceLevel(product.getServiceLevel());
        quote.setElectronic(product.getElectronic());
        quote.setCosmetic(product.getCosmetic());
        quote.setClothing(product.getClothing());
        quote.setLiquid(product.getLiquid());
        quote.setLarge(product.getLarge());
        quote.setTaxInclude(product.getTaxInclude());

        return quote;
    }

    /**
     * 计算费用详情BO
     */
    private ShippingQuoteRespBO calculateFeeDetailBO(LogisticsProductDO product,
                                                     LogisticsProductPriceDO priceRule,
                                                     ShippingCalculationReqBO reqVO,
                                                     Integer volumeWeight,
                                                     Integer chargeableWeight) {
        ShippingQuoteRespBO bo = new ShippingQuoteRespBO();
        // 设置基础信息
        bo.setWeight(reqVO.getWeight());
        bo.setLength(reqVO.getLength());
        bo.setWidth(reqVO.getWidth());
        bo.setHeight(reqVO.getHeight());
        bo.setVolumeWeight(volumeWeight);
        bo.setChargeableWeight(chargeableWeight);
        bo.setNeedVolumeCal(product.getNeedVolumeCal());
        bo.setVolumeBase(priceRule.getVolumeBase());
        //妥投率
        bo.setDeliveryRate(priceRule.getDeliveryRate());


        // 计算基础运费和挂号费
        FeeCalculationResult feeResult = calculateBaseFeeAndRegistration(priceRule, chargeableWeight);

        // 检查计算是否成功，如果失败则返回null（表示该物流渠道不可用）
        if (!feeResult.isCalculationSuccessful()) {
            log.warn("运费计算失败，该物流渠道不可用: {}", feeResult.getFailureReason());
            return null;
        }

        // 设置运费信息
        bo.setFreight(feeResult.getBaseFee() != null ? feeResult.getBaseFee().intValue() : 0);
        bo.setRegistrationFee(feeResult.getRegistrationFee() != null ? feeResult.getRegistrationFee().intValue() : 0);
//        feeDetail.setAdditionalFee(feeResult.getRegistrationFee() != null ? feeResult.getRegistrationFee().intValue() : 0);

        // 设置首重续重信息
        if (priceRule.getFirstUnit() != null) {
            bo.setWeightFirst(priceRule.getFirstUnit());
            if (priceRule.getFirstPrice() != null) {
                bo.setFeeFirst(priceRule.getFirstPrice());
            }
        }

        if (priceRule.getAdditionalUnit() != null) {
            bo.setWeightContinue(priceRule.getAdditionalUnit());
            if (priceRule.getAdditionalPrice() != null) {
                bo.setFeeContinue(priceRule.getAdditionalPrice());
            }
        }

        // 设置附加费用（直接使用Integer）
        //如果阶梯中已经包含挂号费则不再使用price表中挂号费
        if(bo.getRegistrationFee()==null || bo.getRegistrationFee()==0){
            bo.setRegistrationFee(priceRule.getRegistrationFee() != null ? priceRule.getRegistrationFee() : 0);// 挂号费
        }
        bo.setOperationFee(priceRule.getOperationFee() != null ? priceRule.getOperationFee() : 0);// 操作费
        bo.setServiceFee(priceRule.getServiceFee() != null ? priceRule.getServiceFee() : 0); // 服务费
        bo.setCustomsFee(priceRule.getCustomsFee() != null ? priceRule.getCustomsFee() : 0); // 清关费
        bo.setFuelFee(0); // 燃油费 todo
        bo.setAdditionalFee(0); // 附加费 可用于偏于地区附加费 todo

        // 计算总费用
        Integer total = bo.getFreight() +bo.getRegistrationFee() + bo.getOperationFee() + bo.getServiceFee() +
                       bo.getCustomsFee() + bo.getAdditionalFee();
        bo.setTotal(total);

        return bo;
    }

    /**
     * 构建运费报价响应BO
     */
    private void buildQuoteResponseBO(LogisticsProductDO product,
                                                    LogisticsProductPriceDO priceRule,
                                                    ShippingQuoteRespBO quote) {
//        ShippingQuoteRespBO quote = new ShippingQuoteRespBO();

        // 基础产品信息
        quote.setCompanyId(product.getCompanyId());
        quote.setId(product.getId());
        quote.setPriceId(priceRule.getId());
        quote.setZoneCode(priceRule.getZoneCode());
        quote.setName(product.getNameZh());
        quote.setIconUrl(product.getIconUrl());
        quote.setFeatures(product.getFeaturesZh());
        quote.setTransitTime(priceRule.getTransitTime());
        quote.setTaxInclude(product.getTaxInclude());
        quote.setServiceLevel(product.getServiceLevel());
        quote.setElectronic(product.getElectronic());
        quote.setCosmetic(product.getCosmetic());
        quote.setClothing(product.getClothing());
        quote.setLiquid(product.getLiquid());
        quote.setLarge(product.getLarge());
        quote.setRecommended(product.getRecommended());

        // 费用和可用性信息
//        quote.setFeeDetail(feeDetail);
        quote.setAvailable(true);
        quote.setSort(product.getSort());

        // 申报相关信息
//        quote.setMinDeclareValue(product.getMinDeclareValue() != null ? product.getMinDeclareValue().intValue() : null);
//        quote.setMaxDeclareValue(product.getMaxDeclareValue() != null ? product.getMaxDeclareValue().intValue() : null);
        quote.setDefaultDeclareType(product.getDefaultDeclareType());
        quote.setDeclarePerKg(product.getDeclarePerKg() != null ? product.getDeclarePerKg().intValue() : null);
        quote.setDeclareRatio(product.getDeclareRatio());
        quote.setIossEnabled(product.getIossEnabled());
        quote.setFreeInsure(product.getFreeInsure());
        quote.setMinDeclareValue(priceRule.getMinDeclareValue() >0 ? priceRule.getMinDeclareValue() : product.getMinDeclareValue());
        quote.setMaxDeclareValue(priceRule.getMaxDeclareValue() >0 ? priceRule.getMaxDeclareValue() : product.getMaxDeclareValue());


        // 限制信息
//        quote.setRestrictions(buildRestrictionsBO(product, priceRule));
        buildRestrictionsBO(quote,product, priceRule);

//        return quote;
    }

    /**
     * 构建限制信息BO
     */
    private void buildRestrictionsBO(ShippingQuoteRespBO restrictions,LogisticsProductDO product, LogisticsProductPriceDO priceRule) {
//        ShippingQuoteRespBO restrictions = new ShippingQuoteRespBO();
        restrictions.setMinWeight(priceRule.getMinWeight());
        restrictions.setMaxWeight(priceRule.getMaxWeight());
        restrictions.setSizeRestrictions(priceRule.getSizeRestrictions());
        restrictions.setDimensionRestriction(product.getDimensionRestrictionZh()); //todo 尺寸限制描述 语言 待完善
        restrictions.setVolumeWeightRule(product.getVolumeWeightRuleZh());  //todo 体积重规则描述 语言 待完善
        restrictions.setCategoryRestrictions(product.getCategoryRestrictions());
//        return restrictions;
    }

    /**
     * 获取物流公司信息映射
     *
     * @param quotes 运费报价列表
     * @return 物流公司ID到物流公司对象的映射
     */
    private Map<Long, LogisticsCompanyDO> getLogisticsCompanyMap(List<ShippingQuoteRespBO> quotes) {
        Set<Long> companyIds = quotes.stream()
                .map(ShippingQuoteRespBO::getCompanyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(companyIds)) {
            return new HashMap<>();
        }

        List<LogisticsCompanyDO> companies = logisticsCompanyService.getLogisticsCompanyList(companyIds);
        return companies.stream()
                .collect(Collectors.toMap(LogisticsCompanyDO::getId, company -> company));
    }

    /**
     * 将BO转换为用户端响应VO
     *
     * 优化说明：由于AppShippingQuoteRespVO继承自ShippingQuoteRespBO，
     * 使用BeanUtils.toBean进行对象转换，避免大量重复的字段复制代码
     */
    private List<AppShippingQuoteRespVO> convertBOToAppResponse(List<ShippingQuoteRespBO> boQuotes, Map<Long, LogisticsCompanyDO> companyMap) {
        return boQuotes.stream()
                .map(boQuote -> {
                    // 使用BeanUtils进行对象转换，利用继承关系避免字段复制
                    AppShippingQuoteRespVO appQuote = BeanUtils.toBean(boQuote, AppShippingQuoteRespVO.class);

                    // 处理图标URL：如果产品没有图标，则使用物流公司的图标
                    if (appQuote.getIconUrl() == null) {
                        LogisticsCompanyDO company = companyMap.get(boQuote.getCompanyId());
                        if (company != null) {
                            appQuote.setIconUrl(company.getIconUrl());
                        }
                    }

                    return appQuote;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取价格配置JSON
     * 优先使用新的priceConfig字段，如果为空则根据priceType使用对应的旧字段
     *
     * @param priceRule 价格规则
     * @return 价格配置JSON字符串
     */
    private String getPriceConfigJson(LogisticsProductPriceDO priceRule) {
        // 优先使用新的统一配置字段
        if (StrUtil.isNotBlank(priceRule.getPriceConfig())) {
            return priceRule.getPriceConfig();
        }

        // INCREMENTAL类型不需要JSON配置
        return null;
    }

}
