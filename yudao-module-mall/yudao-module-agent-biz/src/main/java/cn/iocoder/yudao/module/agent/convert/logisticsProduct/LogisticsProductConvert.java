package cn.iocoder.yudao.module.agent.convert.logisticsProduct;


import cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo.LogisticsProductSimpleListRespVO;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper
public interface LogisticsProductConvert {

    LogisticsProductConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(LogisticsProductConvert.class);

    @Mappings({
            @Mapping(source = "nameZh", target = "name")
    } )
    List<LogisticsProductSimpleListRespVO> convertList(List<LogisticsProductDO> list);


}
