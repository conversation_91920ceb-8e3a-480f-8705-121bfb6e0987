package cn.iocoder.yudao.module.agent.service.price.calculator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.agent.service.config.AgentConfigService;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateReqBO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateRespBO;
import cn.iocoder.yudao.module.agent.service.shipping.ShippingQuoteService;
import cn.iocoder.yudao.module.agent.service.shipping.bo.LogisticsPlanBO;
import cn.iocoder.yudao.module.agent.service.shipping.bo.ShippingCalculationReqBO;
import cn.iocoder.yudao.module.member.api.address.MemberAddressApi;
import cn.iocoder.yudao.module.member.api.address.dto.MemberAddressRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 运费价格计算实现类
 * @author: DingXiao
 * @create: 2025-04-30 19:29
 **/
@Component
@Order(AgentPriceCalculator.ORDER_DELIVERY)
@Slf4j
public class AgentDeliveryPriceCalculator implements AgentPriceCalculator{

    @Resource
    private MemberAddressApi addressApi;

    @Resource
    private AgentConfigService agentConfigService;

    @Resource
    private ShippingQuoteService shippingQuoteService;

    /**
     * 运费计算（国际物流运费计算 需要先计算总订单的运费再分摊到明细上 而不是明细计算再汇总）
     *
     * @param param 价格计算请求参数
     * @param result 价格计算响应结果
     */
    @Override
    public void calculate(AgentPriceCalculateReqBO param, AgentPriceCalculateRespBO result) {
        log.info("=== 开始计算代购订单运费 ===");
        log.info("计算请求: 用户ID={}, 收货地址ID={}, 商品数量={}", param.getUserId(), param.getAddressId(), CollUtil.size(param.getItems()));

        // 0. 得到收件地址区域
        if (param.getAddressId() == null) {
            // 价格计算时，如果为空就不算~最终下单，会校验该字段不允许空
            return;
        }
        MemberAddressRespDTO address = addressApi.getAddress(param.getAddressId(), param.getUserId());
        Assert.notNull(address, "收件人({})的地址，不能为空", param.getUserId());

        String countryCode = address.getCountryCode();
        if(countryCode == null){
            return;
        }

        // 1. 获取已选中的商品项
        List<AgentPriceCalculateRespBO.Item> parcelItems = result.getItems();
        if (CollUtil.isEmpty(parcelItems)) {
            log.debug("商品列表为空，跳过运费计算");
            return;
        }

        // 2. 计算包裹总重量、体积和尺寸
        PackageInfo packageInfo = calculatePackageInfo(parcelItems);
        log.info("包裹信息: 总重量={}g, 总体积={}cm³, 尺寸=[{}×{}×{}]cm",
                packageInfo.getWeight(), packageInfo.getVolume(),
                packageInfo.getLength(), packageInfo.getWidth(), packageInfo.getHeight());

        if (packageInfo.getWeight() <= 0) {
            log.warn("包裹重量为0，跳过运费计算");
            return;
        }

        // 3. 构建运费查询请求
        ShippingCalculationReqBO reqBO = buildShippingCalculationRequest(countryCode, address, packageInfo, param.getItems());


        // 4. 获取可用的运输方案
        List<LogisticsPlanBO> logisticsPlans = shippingQuoteService.getEnableShippingPlans(reqBO);
        log.info("获取到{}个物流方案", CollUtil.size(logisticsPlans));

        // 没有可用的物流运输方案 直接返回
        if (CollUtil.isEmpty(logisticsPlans)) {
            log.warn("国家{}暂无可用的物流方案", countryCode);
            return;
        }

        // 5. 设置物流方案到结果中
        result.setLogisticsPlans(BeanUtils.toBean(logisticsPlans, AgentPriceCalculateRespBO.LogisticsPlan.class));

        // 6. 选择运费方案并计算运费
        LogisticsPlanBO selectedPlan = selectShippingPlan(logisticsPlans, result.getTransportPlanFeeId());
        if (selectedPlan == null) {
            log.warn("未找到合适的运费方案");
            return;
        }

        int transportPrice = selectedPlan.getTotalFee() != null ? selectedPlan.getTotalFee() : 0;
        log.info("选择的运费方案: {}, 运费: {}分", selectedPlan.getName(), transportPrice);

        if (transportPrice <= 0) {
            log.warn("运费为0或负数，跳过运费计算");
            return;
        }

        // 7. 分摊运费到各个商品项
        log.info("开始分摊运费: 总运费={}分, 商品数量={}", transportPrice, parcelItems.size());
        List<Integer> divideServicePrices = AgentPriceCalculatorHelper.divideWeightPrice(parcelItems, transportPrice);

        // 8. 更新每个商品项的运费
        for (int i = 0; i < parcelItems.size(); i++) {
            AgentPriceCalculateRespBO.Item orderItem = parcelItems.get(i);
            Integer itemDeliveryPrice = divideServicePrices.get(i);
            orderItem.setDeliveryPrice(itemDeliveryPrice);
            AgentPriceCalculatorHelper.recountPayPrice(orderItem);

            log.debug("商品项{}运费分摊: 重量={}g, 分摊运费={}分", i + 1, orderItem.getWeight(), itemDeliveryPrice);
        }

        // 9. 更新订单总运费金额
        result.getPrice().setDeliveryPrice(transportPrice);

        // 10. 重新计算总价
        AgentPriceCalculatorHelper.recountAllPrice(result);

        log.info("=== 代购订单运费计算完成 ===");
        log.info("总运费: {}分, 订单总价: {}分", transportPrice, result.getPrice().getPayPrice());

    }


    /**
     * 验证计算条件
     */
    private boolean validateCalculateConditions(AgentPriceCalculateReqBO param, AgentPriceCalculateRespBO result) {
        if (param.getAddressId() == null) {
            log.debug("收货地址ID为空，跳过运费计算");
            return false;
        }

        if (CollUtil.isEmpty(param.getItems()) || CollUtil.isEmpty(result.getItems())) {
            log.debug("商品列表为空，跳过运费计算");
            return false;
        }

        return true;
    }



    /**
     * 从商品信息中提取分类ID
     */
    private List<Long> extractCategoryIds(List<AgentPriceCalculateReqBO.Item> items) {
        List<Long> categoryIds = new ArrayList<>();

        if (CollUtil.isNotEmpty(items)) {
            for (AgentPriceCalculateReqBO.Item item : items) {
                if (item.getCategoryId() != null) {
                    categoryIds.add(item.getCategoryId());
                }
            }
        }

        // 去重
        return categoryIds.stream().distinct().collect(java.util.stream.Collectors.toList());
    }

    /**
     * 计算包裹信息（重量、体积、尺寸）
     */
    private PackageInfo calculatePackageInfo(List<AgentPriceCalculateRespBO.Item> items) {
        PackageInfo packageInfo = new PackageInfo();

        int totalWeight = 0;
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal maxLength = BigDecimal.ZERO;
        BigDecimal maxWidth = BigDecimal.ZERO;
        BigDecimal totalHeight = BigDecimal.ZERO;

        for (AgentPriceCalculateRespBO.Item item : items) {
            // 计算总重量（包含预包装重量）
            int itemWeight = item.getWeight() != null ? item.getWeight() : 0;
            int prePackageWeight = item.getPrePackageWeight() != null ? item.getPrePackageWeight() : 0;
            totalWeight += (itemWeight + prePackageWeight) * item.getCount();

            // 计算总体积
            if (item.getVolume() != null) {
                totalVolume = totalVolume.add(item.getVolume().multiply(BigDecimal.valueOf(item.getCount())));
            }

            // 计算尺寸（简化处理：取最大长宽，累加高度）
            if (item.getLength() != null && item.getLength().compareTo(maxLength) > 0) {
                maxLength = item.getLength();
            }
            if (item.getWidth() != null && item.getWidth().compareTo(maxWidth) > 0) {
                maxWidth = item.getWidth();
            }
            if (item.getHeight() != null) {
                totalHeight = totalHeight.add(item.getHeight().multiply(BigDecimal.valueOf(item.getCount())));
            }
        }

        packageInfo.setWeight(totalWeight);
        packageInfo.setVolume(totalVolume);
        packageInfo.setLength(maxLength);
        packageInfo.setWidth(maxWidth);
        packageInfo.setHeight(totalHeight);

        return packageInfo;
    }

    /**
     * 构建运费查询请求
     */
    private ShippingCalculationReqBO buildShippingCalculationRequest(String countryCode,
                                                                    MemberAddressRespDTO address,
                                                                    PackageInfo packageInfo,
                                                                    List<AgentPriceCalculateReqBO.Item> items) {
        ShippingCalculationReqBO reqBO = new ShippingCalculationReqBO();
        reqBO.setCountryCode(countryCode);
        reqBO.setPostalCode(address.getPostCode());
        reqBO.setWeight(packageInfo.getWeight());
        reqBO.setLength(packageInfo.getLength());
        reqBO.setWidth(packageInfo.getWidth());
        reqBO.setHeight(packageInfo.getHeight());
        reqBO.setCategoryIds(extractCategoryIds(items));

        return reqBO;
    }

    /**
     * 选择运费方案
     */
    private LogisticsPlanBO selectShippingPlan(List<LogisticsPlanBO> plans, Long selectedPlanFeeId) {
        if (CollUtil.isEmpty(plans)) {
            return null;
        }

        // 如果用户指定了方案，优先使用指定的方案
        if (selectedPlanFeeId != null) {
            for (LogisticsPlanBO plan : plans) {
                if (selectedPlanFeeId.equals(plan.getPriceId()) && Boolean.TRUE.equals(plan.getAvailable())) {
                    log.info("使用用户指定的运费方案: {}", plan.getName());
                    return plan;
                }
            }
            log.warn("用户指定的方案{}不可用，将选择默认方案", selectedPlanFeeId);
        }

        // 选择最便宜的可用方案
        return plans.stream()
                .filter(plan -> Boolean.TRUE.equals(plan.getAvailable()))
                .filter(plan -> plan.getTotalFee() != null && plan.getTotalFee() > 0)
                .min(Comparator.comparing(LogisticsPlanBO::getTotalFee))
                .orElse(null);
    }

    /**
     * 包裹信息内部类
     */
    private static class PackageInfo {
        private Integer weight;
        private BigDecimal volume;
        private BigDecimal length;
        private BigDecimal width;
        private BigDecimal height;

        // getters and setters
        public Integer getWeight() { return weight; }
        public void setWeight(Integer weight) { this.weight = weight; }
        public BigDecimal getVolume() { return volume; }
        public void setVolume(BigDecimal volume) { this.volume = volume; }
        public BigDecimal getLength() { return length; }
        public void setLength(BigDecimal length) { this.length = length; }
        public BigDecimal getWidth() { return width; }
        public void setWidth(BigDecimal width) { this.width = width; }
        public BigDecimal getHeight() { return height; }
        public void setHeight(BigDecimal height) { this.height = height; }
    }

}
