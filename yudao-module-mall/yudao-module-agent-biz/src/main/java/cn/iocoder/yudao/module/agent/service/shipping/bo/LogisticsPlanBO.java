package cn.iocoder.yudao.module.agent.service.shipping.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 配送方案 Response BO
 *
 * <AUTHOR>
 */

@Data
public class LogisticsPlanBO {

    @Schema(description = "物流产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20623")
    private Long productId;

    @Schema(description = "物流产品价格编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20623")
    private Long priceId;

    @Schema(description = "方案名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String name;

    @Schema(description = "方案编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "方案类型(极速，标准，经济)", example = "2")
    private String planType;

    @Schema(description = "运输公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String companyName;

    @Schema(description = "运输方式（空运，陆运，海运等）")
    private String transportMethod;

    @Schema(description = "是否可以带电")
    private Boolean battery;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "基础运费，单位：分", example = "2500")
    private Integer basePrice;

    @Schema(description = "图标URL", example = "https://img1.cnfans.com/xxx.png")
    private String iconUrl;

    @Schema(description = "产品特色描述", example = "1、该线路为三角运输...")
    private String features;

    @Schema(description = "运输时效", example = "12-20")
    private String transitTime;

    @Schema(description = "是否包税", example = "true")
    private Boolean taxInclude;

    @Schema(description = "是否可用", example = "true")
    private Boolean available;

    @Schema(description = "不可用原因", example = "超重")
    private String unavailableReason;

    @Schema(description = "排序", example = "15")
    private Integer sort;

    @Schema(description = "最小重量(g)", example = "0")
    private Integer minWeight;

    @Schema(description = "最大重量(g)", example = "20000")
    private Integer maxWeight;

    @Schema(description = "计费重量(g)", example = "700")
    private Integer chargeableWeight;

    @Schema(description = "总费用(分)", example = "2523")
    private Integer totalFee;

    @Schema(description = "运费(分)", example = "2127")
    private Integer freight;

    @Schema(description = "操作费(分)", example = "237")
    private Integer operationFee;

    @Schema(description = "服务费(分)", example = "158")
    private Integer serviceFee;

    @Schema(description = "清关费(分)", example = "0")
    private Integer customsFee;

    @Schema(description = "燃油费(分)", example = "0")
    private Integer fuelFee;

//    @Schema(description = "空运附加费(分)", example = "0")
//    private Integer airSurcharge;

    @Schema(description = "附加费(分)", example = "0")
    private Integer additionalFee;

    @Schema(description = "是否免费保险", example = "false")
    private Boolean freeInsure;

    //最小申报价值
    @Schema(description = "最小申报价值(分)", example = "0")
    private Integer minDeclareValue;
    //最大申报价值
    @Schema(description = "最大申报价值(分)", example = "0")
    private Integer maxDeclareValue;

    @Schema(description = "妥投率", example = "0.95")
    private BigDecimal deliveryRate;

}
