package cn.iocoder.yudao.module.agent.service.price.calculator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateReqBO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateRespBO;
import cn.iocoder.yudao.module.promotion.api.coupon.CouponApi;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponCodeValidateReqDTO;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponCodeValidateRespDTO;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponRespDTO;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponBusinessTypeEnum;
import cn.iocoder.yudao.module.promotion.enums.common.PromotionDiscountTypeEnum;
import cn.iocoder.yudao.module.promotion.enums.common.PromotionProductScopeEnum;
import cn.iocoder.yudao.module.promotion.enums.common.PromotionTypeEnum;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Predicate;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.filterList;
import static cn.iocoder.yudao.module.trade.enums.ErrorCodeConstants.PRICE_CALCULATE_COUPON_CAN_NOT_USE;

/**
 * @program: ruoyi-vue-pro
 * @description: 优惠券价格计算实现类
 * @author: DingXiao
 * @create: 2025-04-30 19:25
 **/
@Slf4j
@Component
@Order(AgentPriceCalculator.ORDER_COUPON)
public class AgentCouponPriceCalculator implements AgentPriceCalculator{

    @Resource
    private CouponApi couponApi;
    /**
     * 优惠劵价格计算
     *
     * @param param  参数
     * @param result 结果
     */
    @Override
    public void calculate(AgentPriceCalculateReqBO param, AgentPriceCalculateRespBO result) {
        log.debug("-----------------优惠券计算--------[calculate][param({}) result({})]", param, result);

        // 0 是否允许使用优惠券 可以配置在AgentConfig中 todo

        // 1.1 加载用户的优惠劵列表
        List<CouponRespDTO> coupons = couponApi.getCouponListByUserId(param.getUserId(), CouponStatusEnum.UNUSED.getStatus());
        coupons.removeIf(coupon -> LocalDateTimeUtils.beforeNow(coupon.getValidEndTime()));
        // 1.2 计算优惠劵的使用条件
        result.setCoupons(calculateCoupons(coupons, result));

        // 2. 处理优惠码
        if (StrUtil.isNotBlank(param.getCouponCode())) {
            handleCouponCode(param, result);
            return;
        }

        // 3. 校验优惠劵是否可用
        if (param.getCouponId() == null) {
            return;
        }
        AgentPriceCalculateRespBO.Coupon couponBO = CollUtil.findOne(result.getCoupons(), item -> item.getId().equals(param.getCouponId()));
        CouponRespDTO coupon = CollUtil.findOne(coupons, item -> item.getId().equals(param.getCouponId()));
        if (couponBO == null || coupon == null) {
            throw exception(PRICE_CALCULATE_COUPON_CAN_NOT_USE, "优惠劵不存在");
        }
        if (Boolean.FALSE.equals(couponBO.getMatch())) {
            throw exception(PRICE_CALCULATE_COUPON_CAN_NOT_USE, couponBO.getMismatchReason());
        }

        // 3.1 计算可以优惠的金额
        List<AgentPriceCalculateRespBO.Item> orderItems = result.getItems();

        Integer totalPayPrice = AgentPriceCalculatorHelper.calculateTotalPayPrice(orderItems);
        Integer couponPrice = getCouponPrice(coupon, totalPayPrice);
        // 3.2 计算分摊的优惠金额
        List<Integer> divideCouponPrices = AgentPriceCalculatorHelper.divideWeightPrice(orderItems, couponPrice);

        // 4.1 记录使用的优惠劵
        result.setCouponId(param.getCouponId());
        // 4.2 记录优惠明细
        AgentPriceCalculatorHelper.addPromotion(result, orderItems,
                param.getCouponId(), couponBO.getName(), PromotionTypeEnum.COUPON.getType(),
                StrUtil.format("优惠劵：省 {} 元", AgentPriceCalculatorHelper.formatPrice(couponPrice)),
                divideCouponPrices);
        // 4.3 更新 SKU 优惠金额
        for (int i = 0; i < orderItems.size(); i++) {
            AgentPriceCalculateRespBO.Item orderItem = orderItems.get(i);
            orderItem.setCouponPrice(divideCouponPrices.get(i));
            AgentPriceCalculatorHelper.recountPayPrice(orderItem);
        }
        AgentPriceCalculatorHelper.recountAllPrice(result);
    }

    /**
     * 处理优惠码
     */
    private void handleCouponCode(AgentPriceCalculateReqBO param, AgentPriceCalculateRespBO result) {
        // 1. 验证优惠码
        CouponCodeValidateReqDTO validateReq = new CouponCodeValidateReqDTO();
        validateReq.setCouponCode(param.getCouponCode());
        validateReq.setUserId(param.getUserId());
        validateReq.setBusinessType(CouponBusinessTypeEnum.AGENT_ORDER.getType());
        validateReq.setTotalAmount(AgentPriceCalculatorHelper.calculateTotalPayPrice(result.getItems()));

        CouponCodeValidateRespDTO validateResp = couponApi.validateCouponCode(validateReq);
        if (!validateResp.getValid()) {
            throw exception(PRICE_CALCULATE_COUPON_CAN_NOT_USE, validateResp.getInvalidReason());
        }

        // 2. 计算优惠金额和分摊
        List<AgentPriceCalculateRespBO.Item> orderItems = result.getItems();
        Integer couponPrice = validateResp.getDiscountAmount();
        List<Integer> divideCouponPrices = AgentPriceCalculatorHelper.divideWeightPrice(orderItems, couponPrice);

        // 3.1 记录使用的优惠码
        result.setCouponCode(param.getCouponCode());
        // 3.2 记录优惠明细
        AgentPriceCalculatorHelper.addPromotion(result, orderItems,
                validateResp.getTemplateId(), validateResp.getTemplateName(), PromotionTypeEnum.COUPON.getType(),
                StrUtil.format("优惠码：省 {} 元", AgentPriceCalculatorHelper.formatPrice(couponPrice)),
                divideCouponPrices);
        // 3.3 更新 SKU 优惠金额
        for (int i = 0; i < orderItems.size(); i++) {
            AgentPriceCalculateRespBO.Item orderItem = orderItems.get(i);
            orderItem.setCouponPrice(divideCouponPrices.get(i));
            AgentPriceCalculatorHelper.recountPayPrice(orderItem);
        }
        AgentPriceCalculatorHelper.recountAllPrice(result);
    }

    /**
     * 计算用户的优惠劵列表（可用 + 不可用）
     *
     * @param coupons 优惠劵
     * @param result 计算结果
     * @return 优惠劵列表
     */
    private List<AgentPriceCalculateRespBO.Coupon> calculateCoupons(List<CouponRespDTO> coupons,
                                                                    AgentPriceCalculateRespBO result) {
        return convertList(coupons, coupon -> {
            AgentPriceCalculateRespBO.Coupon matchCoupon = BeanUtils.toBean(coupon, AgentPriceCalculateRespBO.Coupon.class);
            // 1.1 优惠劵未到使用时间
            if (LocalDateTimeUtils.afterNow(coupon.getValidStartTime())) {
                return matchCoupon.setMatch(false).setMismatchReason("优惠劵未到使用时间");
            }
            // 1.2 优惠劵没有匹配的商品 代购订单 不检查商品
            //List<AgentPriceCalculateRespBO.Item> orderItems = filterMatchCouponOrderItems(result, coupon);
            //if (CollUtil.isEmpty(orderItems)) {
            //    return matchCoupon.setMatch(false).setMismatchReason("优惠劵没有匹配的商品");
            //}
            // 1.3 差 %1$,.2f 元可用优惠劵
            //Integer totalPayPrice = AgentPriceCalculatorHelper.calculateTotalPayPrice(orderItems);
            //if (totalPayPrice < coupon.getUsePrice()) {
            //    return matchCoupon.setMatch(false)
            //            .setMismatchReason(String.format("差 %1$,.2f 元可用优惠劵", (coupon.getUsePrice() - totalPayPrice) / 100D));
            //}
            // 1.4 优惠金额超过订单金额
            //Integer couponPrice = getCouponPrice(coupon, totalPayPrice);
            //if (couponPrice >= totalPayPrice) {
            //    return matchCoupon.setMatch(false).setMismatchReason("优惠金额超过订单金额");
            //}

            // 2. 满足条件
            return matchCoupon.setMatch(true);
        });
    }

    private Integer getCouponPrice(CouponRespDTO coupon, Integer totalPayPrice) {
        if (PromotionDiscountTypeEnum.PRICE.getType().equals(coupon.getDiscountType())) { // 减价
            return coupon.getDiscountPrice();
        } else if (PromotionDiscountTypeEnum.PERCENT.getType().equals(coupon.getDiscountType())) { // 打折
            int couponPrice = totalPayPrice - (totalPayPrice * coupon.getDiscountPercent() / 100);
            return coupon.getDiscountLimitPrice() == null ? couponPrice
                    : Math.min(couponPrice, coupon.getDiscountLimitPrice()); // 优惠上限
        }
        throw new IllegalArgumentException(String.format("优惠劵(%s) 的优惠类型不正确", coupon));
    }

    /**
     * 获得优惠劵可使用的订单项（商品）列表
     *
     * @param result 计算结果
     * @param coupon 优惠劵
     * @return 订单项（商品）列表
     */
    private List<AgentPriceCalculateRespBO.Item> filterMatchCouponOrderItems(AgentPriceCalculateRespBO result,
                                                                                  CouponRespDTO coupon) {
        Predicate<AgentPriceCalculateRespBO.Item> matchPredicate = AgentPriceCalculateRespBO.Item::getSelected;
        if (PromotionProductScopeEnum.SPU.getScope().equals(coupon.getProductScope())) {
            matchPredicate = matchPredicate // 额外加如下条件
                    .and(orderItem -> coupon.getProductScopeValues().contains(orderItem.getSpuId()));
        } else if (PromotionProductScopeEnum.CATEGORY.getScope().equals(coupon.getProductScope())) {
            matchPredicate = matchPredicate // 额外加如下条件
                    .and(orderItem -> coupon.getProductScopeValues().contains(orderItem.getCategoryId()));
        }
        return filterList(result.getItems(), matchPredicate);
    }

}
