package cn.iocoder.yudao.module.agent.api.serve.dto;

import lombok.Data;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购服务项 Respon DTO
 * @author: DingXiao
 * @create: 2025-05-09 11:49
 **/
@Data
public class AgentServeRespDTO {


    private Long id;
    /**
     * 服务编码
     */
    private String code;
    /**
     * 服务图标
     */
    private String icon;
    /**
     * 类型;保险，免费服务，收费服务
     *
     */
    private Integer type;

    private String name;

    /**
     * 是否免费
     *
     */
    private Boolean free;
    /**
     * 收费金额，单位使用：分
     */
    private Integer price;
}
