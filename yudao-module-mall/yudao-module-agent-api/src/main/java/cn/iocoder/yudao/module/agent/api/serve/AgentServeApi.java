package cn.iocoder.yudao.module.agent.api.serve;

import cn.iocoder.yudao.module.agent.api.serve.dto.AgentServeRespDTO;

import java.util.List;
import java.util.Set;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购服务型API
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-05-09 11:42
 **/
public interface AgentServeApi {

    AgentServeRespDTO getServe(Long id);

    List<AgentServeRespDTO> getListByIds(Set<Long> serverIds, String language);
}
