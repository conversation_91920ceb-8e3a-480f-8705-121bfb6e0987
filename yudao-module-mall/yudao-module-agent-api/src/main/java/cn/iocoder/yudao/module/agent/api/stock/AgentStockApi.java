package cn.iocoder.yudao.module.agent.api.stock;

import cn.iocoder.yudao.module.agent.api.stock.dto.AgentInStockReqDTO;
import cn.iocoder.yudao.module.agent.api.stock.dto.AgentStockPurchaseInReqDTO;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购库存Api
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-04-17 16:12
 **/
public interface AgentStockApi {

    /**
     * 代购采购入库 【erp接口】
     * @param reqDTO 入库信息
     */
    void purchaseInStock(AgentStockPurchaseInReqDTO reqDTO);


    /**
     * 入库 【订单接口】
     * @param inStockReqDTO 入库信息
     * @return 入库id
     */
    Long inStock(AgentInStockReqDTO inStockReqDTO);
}
