package cn.iocoder.yudao.module.product.enums.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OneBound支持的电商平台枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OneBoundPlatformEnum {

    /**
     * 淘宝
     */
    TAOBAO("taobao",
            "淘宝",
            "/taobao/item_search",
            "/taobao/item_get",
            "/taobao/item_fee",
            "/taobao/item_search_img",
            "/taobao/item_get_desc",
            48),

    /**
     * 京东
     */
    JD("jd",
            "京东",
            "/jd/item_search",
            "/jd/item_get",
            "/jd/item_address",
            "/jd/item_search_img",
            "/jd/item_get_desc",
            10),

    /**
     * 1688
     */
    ALIBABA_1688("1688",
            "1688",
            "/1688/item_search",
            "/1688/item_get",
            "/1688/item_fee",
            null,
            null,
            40),

    /**
     * 拼多多
     */
    PINDUODUO("pdd",
            "拼多多",
            "/pinduoduo/item_search",
            "/pinduoduo/item_get",
            null,
            null,
            null,
            20),

    /**
     * 微店
     */
    WEIDIAN("micro",
            "微店",
            "/micro/item_search",
            "/micro/item_get",
            "/micro/item_fee",
            null,
            null,
            20),

    /**
     * 唯品会
     */
    VIP("vip",
            "唯品会",
            "/vip/item_search",
            "/vip/item_get",
            null,
            null,
            null,
            20),

    /**
     * 闲鱼
     */
    XIANYU("goodfish",
            "闲鱼",
            "/goodfish/item_search",
            "/goodfish/item_get",
            null,
            null,
            null,
            20);

    /**
     * 平台代码
     */
    private final String code;

    /**
     * 平台名称
     */
    private final String name;

    /**
     * 搜索接口路径
     */
    private final String searchPath;

    /**
     * 详情接口路径
     */
    private final String detailPath;

    /**
     * 快递费用接口路径
     */
    private final String shippingFeePath;

    /**
     * 以图搜物接口路径
     */
    private final String imageSearchPath;

    /**
     * 描述接口路径
     */
    private final String descPath;

    /**
     * 默认返回结果数量
     */
    private final Integer defaultResultCount;

    /**
     * 根据代码获取平台枚举
     */
    public static OneBoundPlatformEnum getByCode(String code) {
        for (OneBoundPlatformEnum platform : values()) {
            if (platform.getCode().equals(code)) {
                return platform;
            }
        }
        return null;
    }

    /**
     * 是否为有效的平台代码
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 是否支持快递费用查询
     */
    public boolean supportShippingFee() {
        return shippingFeePath != null;
    }

    /**
     * 是否支持以图搜物
     */
    public boolean supportImageSearch() {
        return imageSearchPath != null;
    }

    /**
     * 是否支持商品描述查询
     */
    public boolean supportDesc() {
        return descPath != null;
    }



}
