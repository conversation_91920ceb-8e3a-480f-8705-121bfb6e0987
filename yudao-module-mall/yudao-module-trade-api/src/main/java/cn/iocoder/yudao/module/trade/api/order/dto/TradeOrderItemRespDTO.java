package cn.iocoder.yudao.module.trade.api.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单项 Response DTO
 * @author: DingXiao
 * @create: 2025-04-17 20:55
 **/
@Data
public class TradeOrderItemRespDTO {

    // ========== 订单项基本信息 ==========

    private Long id;

    private Long userId;

    private Long orderId;

    // ========== 商品基本信息 ==========
    private Long categoryId;

    private Long spuId;

    private String spuName;

    private Long skuId;

    //private String properties;
    private List<Property> properties;

    private String picUrl;

    private Integer count;

    // ========== 价格 + 支付基本信息 ==========

    private Integer price;

    private Integer discountPrice;

    private Integer payPrice;

    private Integer orderPartPrice;

    private Integer orderDividePrice;

    // ========== 营销基本信息 ==========

    // TODO 芋艿：在捉摸一下

    // ========== 售后基本信息 ==========

    private Integer afterSaleStatus;

    /**
     * 商品属性
     */
    @Data
    public static class Property implements Serializable {

        /**
         * 属性编号
         *
         * 关联 ProductPropertyDO 的 id 编号
         */
        private Long propertyId;
        /**
         * 属性名字
         *
         * 关联 ProductPropertyDO 的 name 字段
         */
        private String propertyName;

        /**
         * 属性值编号
         *
         * 关联 ProductPropertyValueDO 的 id 编号
         */
        private Long valueId;
        /**
         * 属性值名字
         *
         * 关联 ProductPropertyValueDO 的 name 字段
         */
        private String valueName;

    }
}
