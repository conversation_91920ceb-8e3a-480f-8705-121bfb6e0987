package cn.iocoder.yudao.module.trade.api.order.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单物流信息DTO
 * @author: DingXiao
 * @create: 2025-05-27 11:24
 **/
@Data
public class TradeOrderDeliveryInfoDTO {

    //订单编号
    //private Long orderId;
    //订单项编号
    private Long orderItemId;
    //发货物流公司编号
    private Long logisticsId;
    //发货物流单号
    private String logisticsNo;
    //发货时间
    private LocalDateTime deliveryTime;
}
