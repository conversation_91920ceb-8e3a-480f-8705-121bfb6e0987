package cn.iocoder.yudao.module.trade.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.tenant.config.TenantConfig;
import cn.iocoder.yudao.module.erp.api.product.ErpProductApi;
import cn.iocoder.yudao.module.erp.api.product.dto.ErpProductSaveReqDTO;
import cn.iocoder.yudao.module.erp.api.purchase.ErpPurchaseOrderApi;
import cn.iocoder.yudao.module.erp.api.purchase.dto.ErpPurchaseOrderSaveReqDTO;
import cn.iocoder.yudao.module.erp.api.sale.ErpSaleOrderApi;
import cn.iocoder.yudao.module.erp.api.sale.dto.ErpSaleOrderSaveReqDTO;
import cn.iocoder.yudao.module.erp.enums.ErpAuditStatus;
import cn.iocoder.yudao.module.erp.enums.common.ErpDataSourceEnum;
import cn.iocoder.yudao.module.product.api.spu.ProductSpuApi;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import cn.iocoder.yudao.module.trade.convert.order.TradeOrderItemConvert;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.trade.dal.mysql.order.TradeOrderMapper;
import cn.iocoder.yudao.module.trade.enums.order.TradeOrderAgentStatusEnum;
import cn.iocoder.yudao.module.trade.utils.OrderUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.number.MoneyUtils.fenToYuan;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单代购服务实现类
 * @author: DingXiao
 * @create: 2025-04-15 16:38
 **/
@Service
public class TradeOrderAgentServiceImpl implements TradeOrderAgentService{

    @Resource
    ErpPurchaseOrderApi erpPurchaseOrderApi;

    @Resource
    ErpProductApi erpProductApi;

    @Resource
    ErpSaleOrderApi erpSaleOrderApi;

    @Resource
    TradeOrderQueryService tradeOrderQueryService;

    @Resource
    private TradeOrderMapper tradeOrderMapper;

    @Resource
    ProductSpuApi productSpuApi;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateErpData(TradeOrderDO order, List<TradeOrderItemDO> orderItems, TenantConfig config) {

        //创建产品
        List<ErpProductSaveReqDTO> productList = new ArrayList<>();
        for (TradeOrderItemDO orderItem : orderItems) {
            //ErpProductSaveReqDTO reqDTO = TradeOrderItemConvert.INSTANCE.convert(orderItem);
            ProductSpuRespDTO spuDTO = productSpuApi.getSpu(orderItem.getSpuId());
            ErpProductSaveReqDTO reqDTO = new ErpProductSaveReqDTO()
                    .setName(orderItem.getSpuName())
                    .setBarCode(NumberUtils.generateDateBarcode())
                    .setCategoryId(config.getErpCategoryId())
                    .setStandard(OrderUtil.formattedProperties(orderItem.getProperties()))
                    .setUnitId(config.getErpUnitId())
                    .setStatus(CommonStatusEnum.ENABLE.getStatus())
                    .setSalePrice( fenToYuan(orderItem.getPrice()))
                    .setRemark("agent auto generate")
                    .setCount(orderItem.getCount())
                    .setCreator(config.getErpSaleUserId()+"")
                    .setPicUrl(spuDTO.getPicUrl())
                    .setSourceLink(spuDTO.getSourceLink())
                    .setSource(ErpDataSourceEnum.MALL.getType())
                    .setSourceId(order.getId()) //原商城系统订单信息
                    .setSourceItemId(orderItem.getId())
                    .setSourceSkuId(orderItem.getSkuId());

            Long productId = erpProductApi.createProduct(reqDTO);
            reqDTO.setId(productId);
            productList.add(reqDTO);
        }
        //创建销售订单
        ErpSaleOrderSaveReqDTO reqDTO = new ErpSaleOrderSaveReqDTO()
                .setCustomerId(config.getErpCustomerId())
                .setOrderTime(LocalDateTime.now())
                .setSaleUserId(config.getErpSaleUserId())
                .setAccountId(config.getErpAccountId())
                .setDiscountPercent(BigDecimal.ZERO)
                .setDepositPrice(BigDecimal.ZERO)
                .setStatus(ErpAuditStatus.APPROVE.getStatus()) //默认已审核
                .setRemark("agent auto generate")
                .setCreator(config.getErpSaleUserId()+"")
                .setSource(ErpDataSourceEnum.MALL.getType())
                .setSourceId(order.getId());

        List<ErpSaleOrderSaveReqDTO.Item> saleItems = new ArrayList<>();
        for(ErpProductSaveReqDTO product : productList){
            ErpSaleOrderSaveReqDTO.Item item = new ErpSaleOrderSaveReqDTO.Item()
                    .setProductId(product.getId())
                    .setProductUnitId(product.getUnitId())
                    .setProductPrice(product.getSalePrice())
                    .setCount(BigDecimal.valueOf(product.getCount()))
                    .setSourceItemId(product.getSourceItemId())
                    .setSourceSkuId(product.getSourceSkuId());
            saleItems.add(item);
        }
        reqDTO.setItems(saleItems);
        erpSaleOrderApi.createSaleOrder(reqDTO);

        //创建采购单
        ErpPurchaseOrderSaveReqDTO purchaseReqDTO = new ErpPurchaseOrderSaveReqDTO()
                .setSupplierId(config.getErpSupplierId())
                .setAccountId(config.getErpAccountId())
                .setOrderTime(LocalDateTime.now())
                .setDiscountPercent(BigDecimal.ZERO)
                .setDepositPrice(BigDecimal.ZERO)
                .setRemark("agent auto generate")
                .setCreator(config.getErpSaleUserId()+"")
                .setSource(ErpDataSourceEnum.MALL.getType())
                .setSourceId(order.getId());
        List<ErpPurchaseOrderSaveReqDTO.Item> purchaseItems = new ArrayList<>();
        for(ErpProductSaveReqDTO product : productList){
            ErpPurchaseOrderSaveReqDTO.Item item = new ErpPurchaseOrderSaveReqDTO.Item()
                    .setProductId(product.getId())
                    .setProductUnitId(product.getUnitId())
                    .setProductPrice(product.getSalePrice())
                    .setCount(BigDecimal.valueOf(product.getCount()))
                    .setRemark("agent auto generate")
                    .setSourceItemId(product.getSourceItemId())
                    .setSourceSkuId(product.getSourceSkuId());
            purchaseItems.add(item);
       }
        purchaseReqDTO.setItems(purchaseItems);
        erpPurchaseOrderApi.createPurchaseOrder(purchaseReqDTO);
    }

}
