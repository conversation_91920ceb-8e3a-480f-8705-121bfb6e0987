package cn.iocoder.yudao.module.trade.api.order;

import cn.iocoder.yudao.module.trade.api.order.dto.TradeOrderDeliveryInfoDTO;
import cn.iocoder.yudao.module.trade.api.order.dto.TradeOrderItemRespDTO;
import cn.iocoder.yudao.module.trade.api.order.dto.TradeOrderRespDTO;
import cn.iocoder.yudao.module.trade.convert.order.TradeOrderConvert;
import cn.iocoder.yudao.module.trade.convert.order.TradeOrderItemConvert;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderAgentService;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderQueryService;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 订单 API 接口实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradeOrderApiImpl implements TradeOrderApi {

    @Lazy
    @Autowired
    private TradeOrderUpdateService tradeOrderUpdateService;
    @Resource
    private TradeOrderQueryService tradeOrderQueryService;

    @Resource
    private TradeOrderAgentService tradeOrderAgentService;

    @Override
    public List<TradeOrderRespDTO> getOrderList(Collection<Long> ids) {
        return TradeOrderConvert.INSTANCE.convertList04(tradeOrderQueryService.getOrderList(ids));
    }

    @Override
    public TradeOrderRespDTO getOrder(Long id) {
        return TradeOrderConvert.INSTANCE.convert(tradeOrderQueryService.getOrder(id));
    }

    @Override
    public void cancelPaidOrder(Long userId, Long orderId, Integer cancelType) {
        tradeOrderUpdateService.cancelPaidOrder(userId, orderId, cancelType);
    }


    @Override
    public List<TradeOrderItemRespDTO> getOrderItemList(Long orderId) {
        List<TradeOrderItemDO> orderItemDOList= tradeOrderQueryService.getOrderItemListByOrderId(orderId);
        return TradeOrderItemConvert.INSTANCE.convertDTOList(orderItemDOList);
    }

    @Override
    public TradeOrderItemRespDTO getOrderItem(Long orderItemId) {
        TradeOrderItemDO orderItem = tradeOrderQueryService.getOrderItem(orderItemId);
        return TradeOrderItemConvert.INSTANCE.convertDTO(orderItem);
    }

    @Override
    public void updateDeliveryInfo(TradeOrderDeliveryInfoDTO tradeOrderDeliveryInfoDTO) {
        tradeOrderUpdateService.updateOrderItemDelivery(tradeOrderDeliveryInfoDTO);
    }

    @Override
    public void updateOrderReceive(Long id) {
        tradeOrderUpdateService.updateOrderReceive(id);
    }
}
