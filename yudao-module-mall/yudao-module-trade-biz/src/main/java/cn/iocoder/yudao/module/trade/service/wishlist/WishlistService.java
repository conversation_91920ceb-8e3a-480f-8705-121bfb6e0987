package cn.iocoder.yudao.module.trade.service.wishlist;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.trade.controller.app.wishlist.vo.*;
import cn.iocoder.yudao.module.trade.dal.dataobject.wishlist.WishlistDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 心愿单 Service 接口
 *
 * <AUTHOR>
 */
public interface WishlistService {

    /**
     * 创建心愿单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWishlist(@Valid AppWishlistSaveReqVO createReqVO);

    /**
     * 更新心愿单
     *
     * @param updateReqVO 更新信息
     */
    void updateWishlist(@Valid AppWishlistSaveReqVO updateReqVO);

    /**
     * 删除心愿单
     *
     * @param id 编号
     */
    void deleteWishlist(Long id);

    /**
     * 获得心愿单
     *
     * @param id 编号
     * @return 心愿单
     */
    WishlistDO getWishlist(Long id);

    /**
     * 获得心愿单分页
     *
     * @param pageReqVO 分页查询
     * @return 心愿单分页
     */
    PageResult<WishlistDO> getWishlistPage(AppWishlistPageReqVO pageReqVO);


    /**
     * 添加商品到心愿单
     *
     * @param userId 登录用户编号
     * @param wishlistAddReqVO 添加商品到心愿单请求
     * @return 心愿单编号
     */
    Long addWishlist(Long loginUserId, AppWishlistAddReqVO wishlistAddReqVO);


    /**
     * 删除商品从心愿单
     *
     * @param loginUserId 登录用户编号
     * @param ids 心愿单编号
     */
    void deleteWishlist(Long loginUserId, Collection<Long> ids);

    /**
     * 获得心愿单列表
     *
     * @param loginUserId 登录用户编号
     * @return 心愿单列表
     */
    AppWishlistListRespVO getWishlistList(Long loginUserId);
}