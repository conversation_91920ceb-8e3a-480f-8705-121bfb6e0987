package cn.iocoder.yudao.module.product.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户 App - 获取商品详情的 Request VO
 *
 * <AUTHOR>
 */

@Schema(description = "用户 App - 获取商品详情的 Request VO")
@Data
public class AppProductDescReqVO {

    @Schema(description = "平台代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "taobao")
    private String platform;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String sourceId;

    @Schema(description = "语言代码", example = "zh")
    private String lang;
}
