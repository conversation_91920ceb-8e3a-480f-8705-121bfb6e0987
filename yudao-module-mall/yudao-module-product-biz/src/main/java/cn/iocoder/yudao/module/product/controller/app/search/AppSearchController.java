package cn.iocoder.yudao.module.product.controller.app.search;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.http.UrlUtils;
import cn.iocoder.yudao.module.product.controller.app.search.vo.*;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.enums.search.OneBoundPlatformEnum;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundImageSearchReqDTO;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundImageSearchRespDTO;
import cn.iocoder.yudao.module.product.service.search.ProductSearchContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLang;

/**
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 商品搜索")
@RestController
@RequestMapping("/product/search")
@RequiredArgsConstructor
@Validated
@Slf4j
public class AppSearchController {

    private final ProductSearchContext productSearchContext;

    @GetMapping("/keyword")
    @Operation(summary = "关键词搜索商品")
    @PermitAll
    public CommonResult<PageResult<AppProductSpuRespVO>> searchByKeyword(@Valid AppSearchPageReqVO reqVO) {
        log.info("[searchByKeyword] 关键词搜索开始，请求参数: {}", reqVO);

        try {
            reqVO.setLang(getCurrentLang());
            PageResult<AppProductSpuRespVO> pageResult = productSearchContext.searchProducts(reqVO);
            log.info("[searchByKeyword] 搜索完成，结果: 总数={}, 当前页数据量={}", pageResult.getTotal(), pageResult.getList() != null ? pageResult.getList().size() : 0);

            return success(pageResult);

        } catch (Exception e) {
            log.error("[searchByKeyword] 搜索异常，异常类型: {}, 异常信息: {}", e.getClass().getSimpleName(), e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping("/img")
    @Operation(summary = "以图搜物（POST方式）", description = "支持更复杂的图片搜索参数")
    public CommonResult<PageResult<AppProductSpuRespVO>> searchByImagePost(@Valid AppSearchImgPageReqVO reqVO) {

        reqVO.setLang(getCurrentLang());
        // 调用服务
        PageResult<AppProductSpuRespVO> pageResult = productSearchContext.searchByImg(reqVO);
        return success(pageResult);
    }

    @PostMapping("/detail")
    @Operation(summary = "根据商品ID详情")
    @PermitAll
    public CommonResult<AppProductSpuDetailRespVO> getProductDetail(AppSearchDetailReqVO reqVO) {
        log.info("[getProductDetail] 获取商品详情，请求参数: {}", reqVO);
        reqVO.setLang(getCurrentLang());
        AppProductSpuDetailRespVO detailVO = productSearchContext.getProductDetailById(reqVO);
        return success(detailVO);
    }

    @PostMapping("/url")
    @Operation(summary = "根据商品链接获取详情")
    @PermitAll
    public CommonResult<AppProductSpuDetailRespVO> getProductDetailByUrl(String url) {
        log.info("[getProductDetail] 根据url获取商品详情，请求参数: {}", url);
        //解码 URL 参数
        String decodedUrl = UrlUtils.decodeParam(url);

        AppProductSpuDetailRespVO detailVO = productSearchContext.getProductByUrl(decodedUrl, getCurrentLang());
        return success(detailVO);
    }



    @GetMapping("/shipping-fee")
    @Operation(summary = "获取商品运费")
    public CommonResult<Integer> getProductShippingFee(@Valid AppShippingFeeReqVO reqVO){
        log.info("[getProductPostFee] 获取商品运费");
        Integer postFee = productSearchContext.getShippingFee(reqVO);
        return success(postFee);
    }


    @GetMapping("/desc")
    @Operation(summary = "获取商品描述")
    public CommonResult<String> getProductDesc(@Valid AppProductDescReqVO reqVO){
        log.info("[getProductDesc] 获取商品描述");
        reqVO.setLang(getCurrentLang());
        return success(productSearchContext.getProductDesc(reqVO));
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新商品")
    @PermitAll
    public CommonResult<Boolean> refreshProduct(){

        return success(true);
    }

    @GetMapping("/test")
    @Operation(summary = "测试爬虫系统连接")
    @PermitAll
    public CommonResult<String> testCrawlerConnection() {
        log.info("[testCrawlerConnection] 测试爬虫系统连接");

        try {
            AppSearchPageReqVO testReq = new AppSearchPageReqVO();
            testReq.setKeyword("小叶紫檀");
            testReq.setPlatform("taobao");
            testReq.setLang("zh");
            testReq.setPageNo(1);
            testReq.setPageSize(1);

            PageResult<AppProductSpuRespVO> result = productSearchContext.searchProducts(testReq);
            return success("连接成功，返回数据量: " + (result.getList() != null ? result.getList().size() : 0));

        } catch (Exception e) {
            log.error("[testCrawlerConnection] 连接测试失败", e);
            return success("连接失败: " + e.getMessage());
        }
    }

}
