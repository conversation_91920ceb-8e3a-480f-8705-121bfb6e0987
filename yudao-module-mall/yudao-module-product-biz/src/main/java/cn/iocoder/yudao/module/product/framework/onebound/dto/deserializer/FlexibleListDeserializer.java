package cn.iocoder.yudao.module.product.framework.onebound.dto.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 灵活的List反序列化器
 * 处理空字符串、null、数组等多种情况
 *
 * <AUTHOR>
 */
public class FlexibleListDeserializer extends JsonDeserializer<List<String>> {

    @Override
    public List<String> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken token = p.getCurrentToken();
        
        // 处理null值
        if (token == JsonToken.VALUE_NULL) {
            return new ArrayList<>();
        }
        
        // 处理空字符串
        if (token == JsonToken.VALUE_STRING) {
            String value = p.getValueAsString();
            if (value == null || value.trim().isEmpty()) {
                return new ArrayList<>();
            }
            // 如果是非空字符串，创建包含该字符串的列表
            List<String> result = new ArrayList<>();
            result.add(value);
            return result;
        }
        
        // 处理数组
        if (token == JsonToken.START_ARRAY) {
            List<String> result = new ArrayList<>();
            while (p.nextToken() != JsonToken.END_ARRAY) {
                if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
                    String value = p.getValueAsString();
                    if (value != null && !value.trim().isEmpty()) {
                        result.add(value);
                    }
                }
            }
            return result;
        }
        
        // 其他情况返回空列表
        return new ArrayList<>();
    }
}
