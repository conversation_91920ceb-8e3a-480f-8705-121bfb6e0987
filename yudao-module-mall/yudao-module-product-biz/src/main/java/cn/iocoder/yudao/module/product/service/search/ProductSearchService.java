package cn.iocoder.yudao.module.product.service.search;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchDetailReqVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;


public interface ProductSearchService {


    /**
     * 搜索商品
     *
     * @param reqVO 搜索请求
     * @return 搜索结果
     */
    PageResult<AppProductSpuRespVO> searchProducts(AppSearchPageReqVO reqVO);

    /**
     * 根据商品链接获取详情
     *
     * @param url 商品链接
     * @return 商品详情
     */
    AppProductSpuDetailRespVO getProductByUrl(String url,String lang);

    /**
     * 根据商品ID获取商品详情
     *
     * @param reqVO 详情请求
     * @return 详情
     */
    AppProductSpuDetailRespVO getProductDetailById(AppSearchDetailReqVO reqVO);

    /**
     * 根据商品ID获取商品详情
     *
     * @param source 来源
     * @param sourceId 来源商品ID
     * @return 详情
     */
    AppProductSpuDetailRespVO getProductDetailBySourceId(String sourceId, String source, String lang);

}
