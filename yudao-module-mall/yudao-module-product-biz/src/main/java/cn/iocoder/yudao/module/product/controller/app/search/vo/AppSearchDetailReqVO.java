package cn.iocoder.yudao.module.product.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户 App - 商品详情搜索 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 App - 商品详情搜索 Request VO")
@Data
public class AppSearchDetailReqVO {

    @Schema(description = "商品链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://item.taobao.com/item.htm?id=123456789")
    //@NotBlank(message = "商品链接不能为空")
    //@Size(min = 10, max = 500, message = "商品链接长度必须在10-500字符之间")
    private String productUrl;

    @Schema(description = "语言代码", example = "zh")
    private String lang = "zh";

    @Schema(description = "商品ID", example = "123456")
    private String id;

    @Schema(description = "电商平台", example = "taobao")
    private String platform;

}
