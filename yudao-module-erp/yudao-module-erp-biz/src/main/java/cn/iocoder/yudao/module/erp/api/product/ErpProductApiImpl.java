package cn.iocoder.yudao.module.erp.api.product;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.module.erp.api.product.dto.ErpProductSaveReqDTO;
import cn.iocoder.yudao.module.erp.controller.admin.product.vo.product.ProductSaveReqVO;
import cn.iocoder.yudao.module.erp.service.product.ErpProductService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @program: ruoyi-vue-pro
 * @description: ERP商品API实现类
 * @author: DingXiao
 * @create: 2025-04-15 17:04
 **/
@Service
public class ErpProductApiImpl implements ErpProductApi{

    @Resource
    private ErpProductService erpProductService;

    @Override
    public Long createProduct(ErpProductSaveReqDTO reqDTO) {

        ProductSaveReqVO reqVO = BeanUtil.toBean(reqDTO, ProductSaveReqVO.class);
        return erpProductService.createProduct(reqVO);
    }
}
