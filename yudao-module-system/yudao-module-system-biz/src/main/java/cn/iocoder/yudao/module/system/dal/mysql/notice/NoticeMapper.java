package cn.iocoder.yudao.module.system.dal.mysql.notice;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.notice.vo.NoticePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.notice.vo.AppNoticeReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface NoticeMapper extends BaseMapperX<NoticeDO> {

    default PageResult<NoticeDO> selectPage(NoticePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeDO>()
                .likeIfPresent(NoticeDO::getTitleEn, reqVO.getTitleZh())
                .eqIfPresent(NoticeDO::getStatus, reqVO.getStatus())
                .orderByDesc(NoticeDO::getId));
    }

    default List<NoticeDO> getList(AppNoticeReqVO reqVO){
        // 创建查询条件
        LambdaQueryWrapperX<NoticeDO> queryWrapper = new LambdaQueryWrapperX<NoticeDO>()
                .eqIfPresent(NoticeDO::getType, reqVO.getType())
                .orderByDesc(NoticeDO::getId);

        // 如果 reqVO.getSize() 不为空，则限制返回的记录条数
        if (reqVO.getSize() != null && reqVO.getSize() > 0) {
            queryWrapper.last("LIMIT " + reqVO.getSize());
        }

        // 查询并返回结果
        return selectList(queryWrapper);
    }

    default PageResult<NoticeDO> selectAppPage(AppNoticePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeDO>()
                .eqIfPresent(NoticeDO::getType, reqVO.getType())
                .eqIfPresent(NoticeDO::getStatus, reqVO.getStatus())
                .orderByDesc(NoticeDO::getId));
    }
}
