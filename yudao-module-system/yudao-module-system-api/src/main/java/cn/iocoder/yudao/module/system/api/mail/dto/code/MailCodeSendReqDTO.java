package cn.iocoder.yudao.module.system.api.mail.dto.code;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.mail.MailSceneEnum;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 邮件验证码的发送 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class MailCodeSendReqDTO {

    /**
     * 手机号
     */
    @Email
    @NotEmpty(message = "邮箱不能为空")
    private String email;
    /**
     * 发送场景
     */
    @NotNull(message = "发送场景不能为空")
    @InEnum(MailSceneEnum.class)
    private Integer scene;
    /**
     * 发送 IP
     */
    @NotEmpty(message = "发送 IP 不能为空")
    private String createIp;

    /**
     * 邮箱模板编号前缀
     */
    private String mailTemplateCode;

    /**
     * 邮箱模板参数
     */
    private Map<String,Object> mailTemplateParams;


}
