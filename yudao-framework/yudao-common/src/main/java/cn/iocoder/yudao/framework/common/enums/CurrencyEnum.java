package cn.iocoder.yudao.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: ruoyi-vue-pro
 * @description: 币别
 * @author: DingXiao
 * @create: 2025-05-12 14:39
 **/
@Getter
@AllArgsConstructor
public enum CurrencyEnum {
    DEFAULT("CNY","默认"),
    CNY("CNY","人民币"),
    USD("USD","美元"),
    EUR("EUR","欧元"),
    GBP("GBP","英镑"),
    JPY("JPY","日元"),
    AUD("AUD","澳大利亚元"),
    CAD("CAD","加拿大元"),
    CHF("CHF","瑞士法郎"),
    HKD("HKD","港币"),
    INR("INR","印度卢比"),
    SGD("SGD","新加坡元"),
    THB("THB","泰铢"),
    MYR("MYR","马来西亚林吉特"),
    PHP("PHP","菲律宾比索"),
    IDR("IDR","印度尼西亚盾"),
    KRW("KRW","韩元"),
    TWD("TWD","新台币"),
    VND("VND","越南盾"),
    NZD("NZD","新西兰元"),
    ZAR("ZAR","南非兰特"),
    TRY("TRY","土耳其里拉"),
    MXN("MXN","墨西哥比索"),
    BRL("BRL","巴西雷亚尔"),
    ARS("ARS","阿根廷比索"),
    CLP("CLP","智利比索"),
    COP("COP","哥伦比亚比索"),
    PEN("PEN","秘鲁比索"),
    UYU("UYU","乌拉圭比索");

    private final String Code;
    private final String Name;
}
