package cn.iocoder.yudao.framework.web.core.context;

import java.util.Locale;

/**
 * @program: ruoyi-vue-pro
 * @description: 用户语言上下文 Holder
 * @author: DingXiao
 * @create: 2024-11-19 15:22
 **/
public class LanguageContext {

    private static final ThreadLocal<Locale> currentLanguage = new ThreadLocal<>();

    /**
     * 设置当前语言
     *
     * @param language 语言代码，如 "en" 或 "zh-CN"
     */
    public static void setCurrentLanguage(String language) {
        currentLanguage.set(language != null ? Locale.forLanguageTag(language) : Locale.getDefault());
    }

    /**
     * 获取当前语言
     *
     * @return 当前语言，默认为系统默认语言
     */
    public static Locale getCurrentLanguage() {
        return currentLanguage.get() != null ? currentLanguage.get() : Locale.getDefault();
    }

    /**
     * 获取当前语言编码
     *
     * @return 当前语言，默认为系统默认语言
     */
    public static String getCurrentLang() {
        return currentLanguage.get() != null ? currentLanguage.get().getLanguage() : Locale.getDefault().getLanguage();
    }

    /**
     * 清理当前语言
     */
    public static void clear() {
        currentLanguage.remove();
    }
}
