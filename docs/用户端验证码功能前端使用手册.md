# 用户端验证码功能前端使用手册

## 概述

本文档介绍如何在前端使用用户端验证码功能，包括获取验证码图片、验证验证码以及在用户注册时使用验证码。

## API 接口

### 1. 获取验证码图片

**接口地址：** `GET /member/captcha/get-image`

**请求方式：** GET

**请求参数：** 无

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "key": "abc123def456",
    "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  },
  "msg": "操作成功"
}
```

**响应字段说明：**
- `key`: 验证码唯一标识，用于后续验证
- `image`: 验证码图片的Base64编码，可直接用于img标签的src属性

### 2. 验证验证码（可选）

**接口地址：** `POST /member/captcha/validate`

**请求方式：** POST

**请求参数：**
- `key` (string, 必填): 验证码key
- `code` (string, 必填): 用户输入的验证码值

**请求示例：**
```
POST /member/captcha/validate
Content-Type: application/x-www-form-urlencoded

key=abc123def456&code=1234
```

**响应示例：**
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 3. 用户注册（带验证码）

**接口地址：** `POST /member/auth/register-email`

**请求方式：** POST

**请求参数：**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "用户名",
  "captchaKey": "abc123def456",
  "captchaCode": "1234"
}
```

**字段说明：**
- `email`: 用户邮箱（必填）
- `password`: 用户密码（必填，4-16位）
- `name`: 用户名（可选）
- `captchaKey`: 验证码key（验证码开启时必填）
- `captchaCode`: 验证码值（验证码开启时必填）

## 前端实现示例

### Vue.js 示例

```vue
<template>
  <div class="register-form">
    <form @submit.prevent="handleRegister">
      <!-- 邮箱输入 -->
      <div class="form-group">
        <label>邮箱：</label>
        <input 
          type="email" 
          v-model="form.email" 
          required 
          placeholder="请输入邮箱"
        />
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label>密码：</label>
        <input 
          type="password" 
          v-model="form.password" 
          required 
          minlength="4"
          maxlength="16"
          placeholder="请输入密码（4-16位）"
        />
      </div>

      <!-- 用户名输入 -->
      <div class="form-group">
        <label>用户名：</label>
        <input 
          type="text" 
          v-model="form.name" 
          placeholder="请输入用户名（可选）"
        />
      </div>

      <!-- 验证码 -->
      <div class="form-group captcha-group">
        <label>验证码：</label>
        <input 
          type="text" 
          v-model="form.captchaCode" 
          required 
          placeholder="请输入验证码"
          maxlength="4"
        />
        <img 
          :src="captchaImage" 
          @click="refreshCaptcha" 
          alt="验证码"
          title="点击刷新验证码"
          class="captcha-image"
        />
      </div>

      <button type="submit" :disabled="loading">
        {{ loading ? '注册中...' : '注册' }}
      </button>
    </form>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'RegisterForm',
  data() {
    return {
      form: {
        email: '',
        password: '',
        name: '',
        captchaKey: '',
        captchaCode: ''
      },
      captchaImage: '',
      loading: false
    }
  },
  mounted() {
    this.getCaptcha()
  },
  methods: {
    // 获取验证码
    async getCaptcha() {
      try {
        const response = await axios.get('/member/captcha/get-image')
        if (response.data.code === 0) {
          this.captchaImage = response.data.data.image
          this.form.captchaKey = response.data.data.key
        } else {
          this.$message.error('获取验证码失败')
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
        this.$message.error('获取验证码失败')
      }
    },

    // 刷新验证码
    refreshCaptcha() {
      this.form.captchaCode = ''
      this.getCaptcha()
    },

    // 处理注册
    async handleRegister() {
      if (this.loading) return

      // 基本验证
      if (!this.form.email || !this.form.password || !this.form.captchaCode) {
        this.$message.error('请填写必填项')
        return
      }

      this.loading = true

      try {
        const response = await axios.post('/member/auth/register-email', this.form)
        
        if (response.data.code === 0) {
          this.$message.success('注册成功')
          // 处理注册成功逻辑，如跳转到登录页面或自动登录
          this.$router.push('/login')
        } else {
          this.$message.error(response.data.msg || '注册失败')
          // 注册失败时刷新验证码
          this.refreshCaptcha()
        }
      } catch (error) {
        console.error('注册失败:', error)
        this.$message.error('注册失败，请重试')
        // 注册失败时刷新验证码
        this.refreshCaptcha()
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.register-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.captcha-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.captcha-group input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 40px;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  width: 100%;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}
</style>
```

### React 示例

```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const RegisterForm = () => {
  const [form, setForm] = useState({
    email: '',
    password: '',
    name: '',
    captchaKey: '',
    captchaCode: ''
  });
  const [captchaImage, setCaptchaImage] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getCaptcha();
  }, []);

  // 获取验证码
  const getCaptcha = async () => {
    try {
      const response = await axios.get('/member/captcha/get-image');
      if (response.data.code === 0) {
        setCaptchaImage(response.data.data.image);
        setForm(prev => ({ ...prev, captchaKey: response.data.data.key }));
      } else {
        alert('获取验证码失败');
      }
    } catch (error) {
      console.error('获取验证码失败:', error);
      alert('获取验证码失败');
    }
  };

  // 刷新验证码
  const refreshCaptcha = () => {
    setForm(prev => ({ ...prev, captchaCode: '' }));
    getCaptcha();
  };

  // 处理表单输入
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  // 处理注册
  const handleRegister = async (e) => {
    e.preventDefault();
    
    if (loading) return;

    // 基本验证
    if (!form.email || !form.password || !form.captchaCode) {
      alert('请填写必填项');
      return;
    }

    setLoading(true);

    try {
      const response = await axios.post('/member/auth/register-email', form);
      
      if (response.data.code === 0) {
        alert('注册成功');
        // 处理注册成功逻辑
        window.location.href = '/login';
      } else {
        alert(response.data.msg || '注册失败');
        refreshCaptcha();
      }
    } catch (error) {
      console.error('注册失败:', error);
      alert('注册失败，请重试');
      refreshCaptcha();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="register-form">
      <form onSubmit={handleRegister}>
        <div className="form-group">
          <label>邮箱：</label>
          <input
            type="email"
            name="email"
            value={form.email}
            onChange={handleInputChange}
            required
            placeholder="请输入邮箱"
          />
        </div>

        <div className="form-group">
          <label>密码：</label>
          <input
            type="password"
            name="password"
            value={form.password}
            onChange={handleInputChange}
            required
            minLength="4"
            maxLength="16"
            placeholder="请输入密码（4-16位）"
          />
        </div>

        <div className="form-group">
          <label>用户名：</label>
          <input
            type="text"
            name="name"
            value={form.name}
            onChange={handleInputChange}
            placeholder="请输入用户名（可选）"
          />
        </div>

        <div className="form-group captcha-group">
          <label>验证码：</label>
          <input
            type="text"
            name="captchaCode"
            value={form.captchaCode}
            onChange={handleInputChange}
            required
            placeholder="请输入验证码"
            maxLength="4"
          />
          <img
            src={captchaImage}
            onClick={refreshCaptcha}
            alt="验证码"
            title="点击刷新验证码"
            className="captcha-image"
          />
        </div>

        <button type="submit" disabled={loading}>
          {loading ? '注册中...' : '注册'}
        </button>
      </form>
    </div>
  );
};

export default RegisterForm;
```

## 注意事项

1. **验证码有效期**：验证码默认有效期为5分钟，过期后需要重新获取。

2. **验证码区分大小写**：验证码不区分大小写，用户输入时会自动转换。

3. **验证码使用后失效**：验证码验证成功后会自动删除，不能重复使用。

4. **错误处理**：
   - 验证码错误时，建议刷新验证码
   - 网络错误时，建议重试获取验证码
   - 注册失败时，建议刷新验证码

5. **配置开关**：
   - 可通过配置 `yudao.member.captcha.enable` 来控制是否启用验证码功能
   - 当验证码功能关闭时，注册接口仍然可以正常使用，只是不会验证验证码

6. **样式自定义**：
   - 验证码图片尺寸为 120x40 像素
   - 可根据实际需求调整样式和布局

## 测试建议

1. 测试正常注册流程
2. 测试验证码错误的情况
3. 测试验证码过期的情况
4. 测试网络异常的情况
5. 测试验证码功能开关的效果
