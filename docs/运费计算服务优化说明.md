# 运费计算服务优化说明

## 概述

本次优化主要针对 `ShippingQuoteServiceImpl` 运费计算服务进行了以下两个方面的增强：

1. **增加尺寸限制检查功能**
2. **优化体积重计算和重量比较逻辑**

## 优化内容详情

### 1. 尺寸限制检查功能

#### 新增方法
- `checkSizeRestrictions()` - 检查包裹尺寸是否符合物流产品的限制规则

#### 功能特点
- 支持多种尺寸限制规则（长度、宽度、高度、单边最大、周长、体积等）
- 使用 `SizeRestrictionUtil.checkSizeWithNewValidator()` 进行校验
- 兼容新旧两种尺寸限制配置格式
- 异常情况下不阻止运费计算，确保系统稳定性

#### 集成位置
在 `calculateQuoteForProductBO()` 方法中，位于重量检查之后、分类限制检查之前

### 2. 体积重计算优化

#### 动态体积基数支持
- **优先级规则**：`LogisticsProductPriceDO.volumeBase` > `LogisticsProductDO.volumeBase` > `8000`(默认)
- **新增方法**：`getVolumeBase()` - 获取体积重计算基数
- **优化方法**：`calculateVolumeWeight()` - 使用动态基数计算体积重

#### 计算公式
```
体积重(g) = 长(cm) × 宽(cm) × 高(cm) ÷ volumeBase × 1000
```

### 3. 重量比较规则增强

#### 新增枚举类
`WeightCompareTypeEnum` - 定义重量比较规则

#### 支持的比较规则

##### a. MAX规则（默认）
- **描述**：包裹实际重量和体积重量相比，取较大者计算
- **代码**：`MAX`
- **计算逻辑**：`Math.max(actualWeight, volumeWeight)`

##### b. DOUBLE_THRESHOLD规则
- **描述**：体积重低于实际重量2倍的，按照实际重量收费；达到或超过实际重量2倍的，按照体积重量收取
- **代码**：`DOUBLE_THRESHOLD`
- **计算逻辑**：
  ```java
  if (volumeWeight < actualWeight * 2) {
      return actualWeight;  // 使用实际重量
  } else {
      return volumeWeight;  // 使用体积重
  }
  ```

#### 新增方法
- `calculateChargeableWeight()` - 根据规则计算最终计费重量

## 修改的文件列表

### 1. 核心服务类
- `ShippingQuoteServiceImpl.java` - 运费计算服务主类

### 2. 新增枚举类
- `WeightCompareTypeEnum.java` - 重量比较类型枚举

### 3. 测试类
- `ShippingQuoteServiceImplTest.java` - 单元测试类

### 4. 文档
- `运费计算服务优化说明.md` - 本文档

## 使用示例

### 数据库配置示例

#### LogisticsProductPriceDO 表字段配置

```sql
-- 体积重计算基数（优先使用）
UPDATE agent_logistics_product_price 
SET volume_base = 6000 
WHERE id = 1;

-- 重量比较规则
UPDATE agent_logistics_product_price 
SET weight_compare_type = 'DOUBLE_THRESHOLD' 
WHERE id = 1;

-- 尺寸限制配置
UPDATE agent_logistics_product_price 
SET size_restrictions = '{"maxLength":120,"maxWidth":60,"maxHeight":60,"maxGirth":300,"maxSingleSide":120}' 
WHERE id = 1;
```

#### LogisticsProductDO 表字段配置

```sql
-- 产品级别的体积重计算基数（备用）
UPDATE agent_logistics_product 
SET volume_base = 8000 
WHERE id = 1;
```

### 计算示例

#### 示例1：MAX规则
- 实际重量：1000g
- 体积重：1500g
- 规则：MAX
- **结果**：1500g（取较大者）

#### 示例2：DOUBLE_THRESHOLD规则
- 实际重量：1000g
- 体积重：1500g
- 规则：DOUBLE_THRESHOLD
- **判断**：1500 < 1000 × 2 = 2000
- **结果**：1000g（使用实际重量）

#### 示例3：DOUBLE_THRESHOLD规则
- 实际重量：1000g
- 体积重：2500g
- 规则：DOUBLE_THRESHOLD
- **判断**：2500 ≥ 1000 × 2 = 2000
- **结果**：2500g（使用体积重）

## 向后兼容性

1. **默认行为**：如果未配置 `weightCompareType`，默认使用 MAX 规则
2. **体积基数**：如果未配置 `volumeBase`，默认使用 8000
3. **尺寸限制**：如果未配置 `sizeRestrictions`，跳过尺寸检查
4. **异常处理**：所有新增功能都有异常保护，不会影响现有运费计算流程

## 测试建议

1. **单元测试**：运行 `ShippingQuoteServiceImplTest` 验证枚举功能
2. **集成测试**：使用不同的配置组合测试运费计算
3. **边界测试**：测试空值、零值、异常配置等边界情况
4. **性能测试**：验证优化后的计算性能

## 扩展性

### 新增重量比较规则
如需新增重量比较规则，只需：
1. 在 `WeightCompareTypeEnum` 中添加新的枚举值
2. 实现对应的计算逻辑
3. 添加相应的测试用例

### 新增尺寸限制规则
尺寸限制功能基于 `SizeLimitRule` 和 `SizeRestrictionUtil`，支持灵活的规则配置和扩展。
