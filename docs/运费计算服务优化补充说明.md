# 运费计算服务优化补充说明

## 概述

本次优化主要解决了两个重要问题：
1. 新增泡重比规则（BUBBLE_RATIO）的重量比较类型
2. 优化阶梯计算失败时的处理逻辑，确保不可用的物流渠道正确标记为不可用

## 1. 新增泡重比规则（BUBBLE_RATIO）

### 规则说明
针对不同重量范围的货物采用不同的计费策略：

#### ≤2kg货物
- **泡重比 ≤ 1.5**：免泡，按实际重量计费
- **泡重比 > 1.5**：按包裹实际重量和体积重量相比，取较大者计费

#### >2kg货物
- 直接按包裹实际重量和体积重量相比，取较大者计费

#### 泡重比计算公式
```
泡重比 = 体积重量 / 实际重量
体积重量 = (长 × 宽 × 高 cm) / volumeBase × 1000 (g)
```

### 代码实现

#### WeightCompareTypeEnum 枚举新增
```java
/**
 * 泡重比规则
 * 针对≤2kg货物，若泡重比≤1.5，免泡，按实际重量计费；若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费
 * 针对>2kg货物，按包裹实际重量和体积重量相比，取较大者计费
 * (泡比计算方式与行业通用标准一致：（长*宽*高）/8000/实重)
 */
BUBBLE_RATIO("BUBBLE_RATIO", "泡重比规则", 
            "针对≤2kg货物，若泡重比≤1.5，免泡，按实际重量计费；若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费；针对>2kg货物，按包裹实际重量和体积重量相比，取较大者计费");
```

#### 计算逻辑实现
```java
private Integer calculateBubbleRatioWeight(Integer actualWeight, Integer volumeWeight) {
    // 针对>2kg货物，直接按包裹实际重量和体积重量相比，取较大者计费
    if (actualWeight > 2000) {
        return Math.max(actualWeight, volumeWeight);
    }
    
    // 针对≤2kg货物，需要计算泡重比
    // 泡重比 = 体积重 / 实际重量
    double bubbleRatio = (double) volumeWeight / actualWeight;
    
    if (bubbleRatio <= 1.5) {
        // 若泡重比≤1.5，免泡，按实际重量计费
        return actualWeight;
    } else {
        // 若泡重比＞1.5，按包裹实际重量和体积重量相比，取较大者计费
        return Math.max(actualWeight, volumeWeight);
    }
}
```

### 测试用例
所有测试用例均已通过验证：
- ≤2kg货物，泡重比≤1.5：按实际重量计费 ✅
- ≤2kg货物，泡重比>1.5：按较大者计费 ✅
- >2kg货物：直接按较大者计费 ✅
- 边界情况（正好2kg）：正确处理 ✅

## 2. 阶梯计算失败处理优化

### 问题描述
当货物重量不在物流渠道的阶梯配置范围内时，原有逻辑可能返回运费为0，这会误导前端认为该渠道可用且免费。

### 解决方案

#### FeeCalculationResult 类增强
```java
private static class FeeCalculationResult {
    private final BigDecimal baseFee;
    private final BigDecimal registrationFee;
    private final boolean useTieredRegistrationFee;
    private final boolean calculationSuccessful;  // 新增：计算是否成功
    private final String failureReason;           // 新增：失败原因
    
    // 成功构造函数
    public FeeCalculationResult(BigDecimal baseFee, BigDecimal registrationFee, boolean useTieredRegistrationFee) {
        // ...
        this.calculationSuccessful = true;
        this.failureReason = null;
    }
    
    // 失败构造函数
    public FeeCalculationResult(String failureReason) {
        // ...
        this.calculationSuccessful = false;
        this.failureReason = failureReason;
    }
}
```

#### 阶梯计算失败检测
```java
// 阶梯递增价格计算
TieredIncrementalPriceCalculator.TieredIncrementalResult tieredIncrementalResult = 
    TieredIncrementalPriceCalculator.calculateTieredIncrementalPrice(
        priceConfigJson, chargeableWeight, priceRule.getRegistrationFee());

// 检查是否找到匹配的阶梯
if (tieredIncrementalResult.getMatchedTier() == null) {
    String failureReason = String.format("重量%dg不在任何阶梯递增配置范围内", chargeableWeight);
    log.warn("阶梯递增价格计算失败: {}", failureReason);
    return new FeeCalculationResult(failureReason);
}
```

#### 不可用渠道处理
```java
// 检查运费计算是否成功
if (!feeResult.isCalculationSuccessful()) {
    log.warn("运费计算失败，该物流渠道不可用: {}", feeResult.getFailureReason());
    return null; // 返回null表示该物流渠道不可用
}

// 在上层调用中处理null返回值
if (quoteFee == null) {
    log.warn("运费计算失败，该物流渠道不可用: productId={}, priceId={}", 
             priceRule.getProductId(), priceRule.getId());
    return createUnavailableQuoteBO(product, priceRule, "当前重量不在该物流渠道的阶梯配置范围内");
}
```

### 影响范围
- **阶梯递增计算方式**：当重量不在配置范围内时，渠道标记为不可用
- **纯阶梯计算方式**：当重量不在配置范围内时，渠道标记为不可用
- **固定价格计算方式**：不受影响，继续正常工作

## 3. 配置示例

### 数据库配置示例
```sql
-- 物流产品价格规则配置
UPDATE logistics_product_price_do 
SET weight_compare_type = 'BUBBLE_RATIO' 
WHERE id = 1;

-- 确保volumeBase字段有值
UPDATE logistics_product_price_do 
SET volume_base = 8000 
WHERE volume_base IS NULL;
```

### 前端调用示例
前端无需修改，继续调用现有的运费计算接口：
```javascript
// 调用运费计算接口
const response = await getShippingQuotes({
    items: [...],
    dimensions: {...}
});

// 检查返回的报价是否可用
response.data.forEach(quote => {
    if (!quote.available) {
        console.log(`物流渠道不可用: ${quote.unavailableReason}`);
    }
});
```

## 4. 注意事项

1. **向后兼容性**：新增的BUBBLE_RATIO规则不影响现有的MAX和DOUBLE_THRESHOLD规则
2. **默认值处理**：当volumeBase字段为空时，系统会自动使用8000作为默认值
3. **错误处理**：所有计算失败的情况都会被正确捕获并记录日志
4. **性能影响**：新增的计算逻辑对性能影响微乎其微

## 5. 测试建议

建议在以下场景进行测试：
1. 不同重量范围的货物使用BUBBLE_RATIO规则计算
2. 重量超出阶梯配置范围的情况
3. volumeBase字段为空的兼容性测试
4. 混合使用不同重量比较规则的场景

## 6. 后续扩展

该设计支持未来添加更多重量比较规则：
1. 在WeightCompareTypeEnum中添加新的枚举值
2. 在calculateChargeableWeight方法中添加对应的case分支
3. 实现具体的计算逻辑

系统架构具有良好的扩展性，可以轻松适应不同物流服务商的计费规则。
